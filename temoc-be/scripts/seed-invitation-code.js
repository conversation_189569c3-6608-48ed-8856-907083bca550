const { MongoClient } = require('mongodb');
require('dotenv').config();

async function seedInvitationCode() {
  const client = new MongoClient(process.env.MONGODB_URI);
  
  try {
    await client.connect();
    console.log('Connected to MongoDB');
    
    const db = client.db();
    const otpCollection = db.collection('otps');
    
    // Check if invitation code already exists
    const existingCode = await otpCollection.findOne({ otp: 123 });

    if (existingCode) {
      console.log('✅ Invitation code 000123 already exists in database');
      console.log('Existing record:', existingCode);
      return;
    }

    // Insert the fixed invitation code (storing as number 123 which represents 000123)
    const result = await otpCollection.insertOne({
      otp: 123,
      secret: 'invitation-access-key',
      createdAt: new Date(),
      updatedAt: new Date()
    });

    console.log('✅ Successfully added invitation code 000123 to database');
    console.log('Inserted document ID:', result.insertedId);
    console.log('You can now use invitation code: 000123');
    
  } catch (error) {
    console.error('❌ Error seeding invitation code:', error);
  } finally {
    await client.close();
    console.log('Disconnected from MongoDB');
  }
}

// Run the script
seedInvitationCode();
