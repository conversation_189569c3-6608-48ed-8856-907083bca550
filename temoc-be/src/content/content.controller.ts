import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  UseGuards,
  UseInterceptors,
  UploadedFile,
  UploadedFiles,
  Request,
  NotFoundException,
  BadRequestException,
  Logger,
  Query,
  ParseIntPipe,
  DefaultValuePipe,
} from "@nestjs/common";
import {
  FileInterceptor,
  FileFieldsInterceptor,
} from "@nestjs/platform-express";
import { memoryStorage } from "multer";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { ContentService } from "./content.service";
import { CreateLibraryDto } from "./dto/create-library.dto";
import { CreateAlbumDto } from "./dto/create-album.dto";
import { CreateTrackDto } from "./dto/create-track.dto";
import { AuthUser } from "src/auth/decorators/user.decorator";
import { ApiOperation, ApiQuery, ApiResponse } from "@nestjs/swagger";

@Controller("content")
export class ContentController {
  private readonly logger = new Logger(ContentController.name);

  constructor(private readonly contentService: ContentService) {}

  // LIBRARY ENDPOINTS
  @UseGuards(JwtAuthGuard)
  @Post("libraries")
  @UseInterceptors(FileInterceptor("thumbnail", { storage: memoryStorage() }))
  async createLibrary(
    @AuthUser() req,
    @Body() createLibraryDto: CreateLibraryDto,
    @UploadedFile() thumbnail?: Express.Multer.File
  ) {
    this.logger.log(`Creating library for user ${req._id}`);

    // Validate thumbnail if provided
    if (thumbnail && !thumbnail.mimetype.startsWith("image/")) {
      throw new BadRequestException("Thumbnail must be an image");
    }

    const library = await this.contentService.createLibrary(
      req._id,
      createLibraryDto,
      thumbnail
    );

    return {
      success: true,
      message: "Library created successfully",
      library,
    };
  }

  @UseGuards(JwtAuthGuard)
  @Get("libraries")
  async getAllLibraries(@AuthUser() req) {
    this.logger.log(`Getting all libraries for user ${req._id}`);

    const libraries = await this.contentService.findAllLibraries(req._id);

    return {
      success: true,
      libraries,
    };
  }

  @UseGuards(JwtAuthGuard)
  @Get("libraries/:id")
  async getLibraryById(@AuthUser() req, @Param("id") id: string) {
    this.logger.log(`Getting library ${id} for user ${req._id}`);

    const library = await this.contentService.findLibraryById(id, req._id);

    return {
      success: true,
      library,
    };
  }

  @UseGuards(JwtAuthGuard)
  @Put("libraries/:id")
  @UseInterceptors(FileInterceptor("thumbnail", { storage: memoryStorage() }))
  async updateLibrary(
    @Request() req,
    @Param("id") id: string,
    @Body() updateLibraryDto: any,
    @UploadedFile() thumbnail?: Express.Multer.File
  ) {
    this.logger.log(`Updating library ${id} for user ${req.user.sub}`);

    // Validate thumbnail if provided
    if (thumbnail && !thumbnail.mimetype.startsWith("image/")) {
      throw new BadRequestException("Thumbnail must be an image");
    }

    const library = await this.contentService.updateLibrary(
      id,
      req.user.sub,
      updateLibraryDto,
      thumbnail
    );

    return {
      success: true,
      message: "Library updated successfully",
      library,
    };
  }

  @UseGuards(JwtAuthGuard)
  @Delete("libraries/:id")
  async deleteLibrary(@AuthUser() req, @Param("id") id: string) {
    this.logger.log(`Deleting library ${id} for user ${req._id}`);

    await this.contentService.deleteLibrary(id, req._id);

    return {
      success: true,
      message: "Library and all its content deleted successfully",
    };
  }

  // ALBUM ENDPOINTS
  @UseGuards(JwtAuthGuard)
  @Post("albums")
  @UseInterceptors(FileInterceptor("thumbnail", { storage: memoryStorage() }))
  async createAlbum(
    @AuthUser() req,
    @Body() createAlbumDto: CreateAlbumDto,
    @UploadedFile() thumbnail?: Express.Multer.File
  ) {
    this.logger.log(`Creating album for user ${req._id}`);

    // Validate thumbnail if provided
    if (thumbnail && !thumbnail.mimetype.startsWith("image/")) {
      throw new BadRequestException("Thumbnail must be an image");
    }

    const album = await this.contentService.createAlbum(
      req._id,
      createAlbumDto,
      thumbnail
    );

    return {
      success: true,
      message: "Album created successfully",
      album,
    };
  }

  @UseGuards(JwtAuthGuard)
  @Get("libraries/:libraryId/albums")
  async getAllAlbums(@AuthUser() req, @Param("libraryId") libraryId: string) {
    this.logger.log(
      `Getting all albums in library ${libraryId} for user ${req._id}`
    );

    const albums = await this.contentService.findAllAlbums(libraryId, req._id);

    return {
      success: true,
      albums,
    };
  }

  @UseGuards(JwtAuthGuard)
  @Get("albums/:id")
  async getAlbumById(@Request() req, @Param("id") id: string) {
    this.logger.log(`Getting album ${id} for user ${req.user.sub}`);

    const album = await this.contentService.findAlbumById(id, req.user.sub);

    return {
      success: true,
      album,
    };
  }

  @UseGuards(JwtAuthGuard)
  @Put("albums/:id")
  @UseInterceptors(FileInterceptor("thumbnail", { storage: memoryStorage() }))
  async updateAlbum(
    @AuthUser() req,
    @Param("id") id: string,
    @Body() updateAlbumDto: any,
    @UploadedFile() thumbnail?: Express.Multer.File
  ) {
    this.logger.log(`Updating album ${id} for user ${req._id}`);

    // Validate thumbnail if provided
    if (thumbnail && !thumbnail.mimetype.startsWith("image/")) {
      throw new BadRequestException("Thumbnail must be an image");
    }

    const album = await this.contentService.updateAlbum(
      id,
      req._id,
      updateAlbumDto,
      thumbnail
    );

    return {
      success: true,
      message: "Album updated successfully",
      album,
    };
  }

  @UseGuards(JwtAuthGuard)
  @Delete("albums/:id")
  async deleteAlbum(@AuthUser() req, @Param("id") id: string) {
    this.logger.log(`Deleting album ${id} for user ${req._id}`);

    await this.contentService.deleteAlbum(id, req._id);

    return {
      success: true,
      message: "Album and all its tracks deleted successfully",
    };
  }

  // TRACK ENDPOINTS
  @UseGuards(JwtAuthGuard)
  @Post("tracks")
  @UseInterceptors(
    FileFieldsInterceptor(
      [
        { name: "audioFile", maxCount: 1 },
        { name: "thumbnail", maxCount: 1 },
      ],
      { storage: memoryStorage() }
    )
  )
  async createTrack(
    @AuthUser() req,
    @Body() createTrackDto: CreateTrackDto,
    @UploadedFiles()
    files: {
      audioFile?: Express.Multer.File[];
      thumbnail?: Express.Multer.File[];
    }
  ) {
    //   this.logger.log(`Creating track for user ${req.user.sub}`);

    if (!files?.audioFile?.[0]) {
      throw new BadRequestException("Audio file is required");
    }

    const audioFile = files.audioFile[0];
    const thumbnail = files?.thumbnail?.[0];

    //   Validate audio file
    const validAudioTypes = [
      "audio/aac",
      "audio/midi",
      "audio/x-midi",
      "audio/mpeg",
      "audio/ogg",
      "audio/opus",
      "audio/wav",
      "audio/webm",
      "audio/flac",
      "audio/aiff",
      "audio/x-aiff",
      "audio/alac",
      "audio/mp3",
      "audio/mp4",
      "audio/x-ms-wma",
      "audio/3gpp",
      "audio/3gpp2",
    ];

    if (!validAudioTypes.includes(audioFile.mimetype)) {
      throw new BadRequestException(
        "File must be a valid audio format (WAV, FLAC, AIFF, ALAC, MP3)"
      );
    }

    // Validate thumbnail if provided
    if (thumbnail && !thumbnail.mimetype.startsWith("image/")) {
      throw new BadRequestException("Thumbnail must be an image");
    }

    const track = await this.contentService.createTrack(
      req._id,
      createTrackDto,
      audioFile,
      thumbnail
    );

    return {
      success: true,
      message: "Track created successfully",
      track,
    };
  }

  @UseGuards(JwtAuthGuard)
  @Get("albums/:albumId/tracks")
  async getAllTracks(@AuthUser() req, @Param("albumId") albumId: string) {
    this.logger.log(
      `Getting all tracks in album ${albumId} for user ${req._id}`
    );

    const tracks = await this.contentService.findAllTracks(albumId, req._id);

    return {
      success: true,
      tracks,
    };
  }

  @UseGuards(JwtAuthGuard)
  @Get("tracks/:id")
  async getTrackById(@AuthUser() req, @Param("id") id: string) {
    this.logger.log(`Getting track ${id} for user ${req._id}`);

    const track = await this.contentService.findTrackById(id, req._id);

    return {
      success: true,
      track,
    };
  }

  @UseGuards(JwtAuthGuard)
  @Put("tracks/:id")
  @UseInterceptors(
    FileFieldsInterceptor(
      [
        { name: "audioFile", maxCount: 1 },
        { name: "thumbnail", maxCount: 1 },
      ],
      { storage: memoryStorage() }
    )
  )
  async updateTrack(
    @AuthUser() req,
    @Param("id") id: string,
    @Body() updateTrackDto: any,
    @UploadedFiles()
    files: {
      audioFile?: Express.Multer.File[];
      thumbnail?: Express.Multer.File[];
    }
  ) {
    this.logger.log(`Updating track ${id} for user ${req._id}`);

    const audioFile = files?.audioFile?.[0];
    const thumbnail = files?.thumbnail?.[0];
    console.log(audioFile, thumbnail);

    // Validate audio file if provided
    if (audioFile) {
      const validAudioTypes = [
        "audio/mpeg",
        "audio/wav",
        "audio/flac",
        "audio/aiff",
        "audio/alac",
        "audio/mp3",
        "audio/mp4",
      ];
      if (!validAudioTypes.includes(audioFile.mimetype)) {
        throw new BadRequestException(
          "File must be a valid audio format (WAV, FLAC, AIFF, ALAC, MP3)"
        );
      }
    }

    // Validate thumbnail if provided
    if (thumbnail && !thumbnail.mimetype.startsWith("image/")) {
      throw new BadRequestException("Thumbnail must be an image");
    }

    const track = await this.contentService.updateTrack(
      id,
      req._id,
      updateTrackDto,
      audioFile,
      thumbnail
    );

    return {
      success: true,
      message: "Track updated successfully",
      track,
    };
  }

  @UseGuards(JwtAuthGuard)
  @Delete("tracks/:id")
  async deleteTrack(@AuthUser() req, @Param("id") id: string) {
    this.logger.log(`Deleting track ${id} for user ${req._id}`);

    await this.contentService.deleteTrack(id, req._id);

    return {
      success: true,
      message: "Track deleted successfully",
    };
  }

  // TRACK PLAY COUNT
  @Post("tracks/:id/play")
  async trackPlay(@Param("id") id: string) {
    this.logger.log(`Recording play for track ${id}`);

    await this.contentService.incrementTrackPlayCount(id);

    return {
      success: true,
    };
  }

  // PUBLIC FAN ACCESS ENDPOINTS
  @Get("public/libraries")
  async getPublicLibraries() {
    this.logger.log("Getting public libraries");

    const libraries = await this.contentService.getPublicLibraries();

    return {
      success: true,
      libraries,
    };
  }

  @Get("public/libraries/:libraryId/albums")
  async getPublicAlbums(@Param("libraryId") libraryId: string) {
    this.logger.log(`Getting public albums in library ${libraryId}`);

    const albums = await this.contentService.getPublicAlbums(libraryId);

    return {
      success: true,
      albums,
    };
  }

  @Get("public/albums/:albumId/tracks")
  async getPublicTracks(@Param("albumId") albumId: string) {
    this.logger.log(`Getting public tracks in album ${albumId}`);

    const tracks = await this.contentService.getPublicTracks(albumId);

    return {
      success: true,
      tracks,
    };
  }

  @Get("public/libraries/user/:userId")
  async getPublicLibrariesByUser(@Param("userId") userId: string) {
    this.logger.log(`Getting public libraries for user ${userId}`);

    const libraries =
      await this.contentService.getPublicLibrariesByUser(userId);

    return {
      success: true,
      libraries,
    };
  }

  @Get("public/albums/user/:userId")
  async getPublicAlbumsByUser(@Param("userId") userId: string) {
    this.logger.log(
      `Getting public albums with track counts for user ${userId}`
    );

    const albums = await this.contentService.getPublicAlbumsByUser(userId);

    return {
      success: true,
      albums,
    };
  }

  @Get("public/tracks/user/:userId")
  async getPublicTracksByUser(@Param("userId") userId: string) {
    this.logger.log(`Getting public tracks for user ${userId}`);

    const tracks = await this.contentService.getPublicTracksByUser(userId);

    return {
      success: true,
      tracks,
    };
  }

  @Post("update-access")
  updateAccess(@Body() body: any) {
    return this.contentService.updateAlbumAccess(body);
  }

  @Get("creator/:creatorId")
  async getAlbumsByCreator(@Param("creatorId") creatorId: string) {
    return this.contentService.findByCreatorId(creatorId);
  }

  @Get("trending-tracks")
  @ApiOperation({ summary: "Get trending tracks" })
  @ApiQuery({
    name: "limit",
    required: false,
    type: Number,
    description: "Number of tracks to return",
  })
  @ApiResponse({
    status: 200,
    description: "Trending tracks retrieved successfully",
  })
  async getTrendingTracks(
    @Query("limit", new DefaultValuePipe(10), ParseIntPipe) limit: number
  ) {
    return this.contentService.getTrendingTracks(limit);
  }
}
