import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

@Schema({ timestamps: true })
export class TrackLike extends Document {
  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'User', required: true })
  user: MongooseSchema.Types.ObjectId;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'Track', required: true })
  track: MongooseSchema.Types.ObjectId;

  @Prop({ default: true })
  isActive: boolean;
}

export const TrackLikeSchema = SchemaFactory.createForClass(TrackLike);

// Create compound index for efficient queries and prevent duplicates
TrackLikeSchema.index({ user: 1, track: 1 }, { unique: true });
TrackLikeSchema.index({ track: 1 });
TrackLikeSchema.index({ user: 1 });
