import {
  BadRequestException,
  Injectable,
  Logger,
  NotFoundException,
} from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import { User, UserRole } from "./schemas/user.schema";
import { DynamicApiService } from "../dynamic/dynamic-api.service";
import { CloudinaryService } from "../cloudinary/cloudinary.service";
import { UpdateArtistProfileDto } from "./dto/update-artist-profile.dto";
import { AddSocialLinkDto } from "./dto/add-social-link.dto";
import { UpdateSocialLinkDto } from "./dto/update-social-link.dto";
// import { BasicInfoDto } from "./dto/basic-info.dto";
// import { UsernameDto } from "./dto/username.dto";
// import { SocialLinksDto } from "./dto/social-links.dto";
// import { DemoTracksDto } from "./dto/demo-tracks.dto";
import { ConflictException } from "@nestjs/common";
import {
  BasicInfoDto,
  DemoTracksDto,
  SocialLinkDto,
  SocialLinksDto,
  UsernameDto,
} from "./dto/artist-application.dto";
import { EmailService } from "../email/email.service";
import { Otp, OtpDocument } from "./schemas/otp.schema";
import { VerifyOtpDto } from "./dto/verify-otp.dto";

@Injectable()
export class UsersService {
  private readonly logger = new Logger(UsersService.name);

  constructor(
    @InjectModel(User.name) private userModel: Model<User>,
    @InjectModel("Track") private trackModel: Model<any>,
    @InjectModel("Album") private albumModel: Model<any>,
    @InjectModel("Library") private libraryModel: Model<any>,
    @InjectModel(Otp.name) private otpModel: Model<OtpDocument>,
    private dynamicApiService: DynamicApiService,
    private cloudinaryService: CloudinaryService,
    private emailService: EmailService
  ) {}

  async create(createUserDto: any): Promise<User> {
    this.logger.log(`Creating new user: ${createUserDto.email}`);
    const createdUser = new this.userModel(createUserDto);
    return createdUser.save();
  }

  async findAll(): Promise<User[]> {
    return this.userModel.find().exec();
  }

  async findByRole(role: UserRole): Promise<User[]> {
    return this.userModel.find({ role }).exec();
  }

  async findById(id: string): Promise<User> {
    return this.userModel.findById(id).exec();
  }

  async findByEmail(email: string): Promise<User> {
    return this.userModel.findOne({ email }).exec();
  }

  async findByUsername(username: string): Promise<User> {
    return this.userModel.findOne({ username }).exec();
  }

  async findByDynamicUserId(dynamicUserId: string): Promise<User> {
    return this.userModel.findOne({ dynamicUserId }).exec();
  }

  async findByWalletAddress(address: string): Promise<User> {
    // Case-insensitive search for wallet address
    return this.userModel
      .findOne({
        "wallets.address": { $regex: new RegExp(`^${address}$`, "i") },
      })
      .exec();
  }

  // Get artist profile for display in form
  async getArtistProfile(userId: string): Promise<any> {
    const user = await this.findById(userId);

    if (!user) {
      throw new NotFoundException("User not found");
    }

    // Return all profile-related fields for form display
    return {
      id: user.id,
      firstName: user.firstName,
      lastName: user.lastName,
      username: user.username,
      email: user.email,
      // avatarUrl: user.avatarUrl,
      role: user.role,
      // Artist profile fields
      bio: user.artistProfile?.bio || "",
      genre: user.artistProfile?.genre || "",
      socialLinks: user.artistProfile?.socialLinks || [],
      coverPhoto: user.artistProfile?.coverPhoto || "",

      profilePic: user.artistProfile?.profilePic || "",
    };
  }
  async setTokenCreated(userId: string, value: boolean): Promise<User> {
    return this.userModel
      .findByIdAndUpdate(userId, { tokenCreated: value }, { new: true })
      .exec();
  }

  async hasCreatedToken(userId: string): Promise<boolean> {
    const user = await this.findById(userId);
    return user ? user.tokenCreated : false;
  }
  async updateArtistProfileComprehensive(
    userId: string,
    profileData: UpdateArtistProfileDto,
    files?: {
      profilePicture?: Express.Multer.File[];
      coverPhoto?: Express.Multer.File[];
    }
  ): Promise<any> {
    this.logger.log(
      `Updating artist profile comprehensively for user ${userId}`
    );

    const user = await this.findById(userId);
    if (!user) {
      throw new NotFoundException("User not found");
    }

    // 1. Create update data object
    const updateData: any = {};
    // 2. Handle user fields updates if provided
    const dynamicUpdateData: any = {};
    let dynamicUpdateNeeded = false;

    if (profileData.firstName !== undefined) {
      updateData.firstName = profileData.firstName;
      dynamicUpdateData.firstName = profileData.firstName;
      dynamicUpdateNeeded = true;
    }

    if (profileData.lastName !== undefined) {
      updateData.lastName = profileData.lastName;
      dynamicUpdateData.lastName = profileData.lastName;
      dynamicUpdateNeeded = true;
    }

    if (profileData.username !== undefined) {
      updateData.username = profileData.username;
      dynamicUpdateData.username = profileData.username;
      dynamicUpdateNeeded = true;
    }

    // 3. Sync with Dynamic if name/username fields changed
    if (user.dynamicUserId && dynamicUpdateNeeded) {
      try {
        const dynamicResult = await this.dynamicApiService.updateUserProfile(
          user.dynamicUserId,
          dynamicUpdateData
        );

        if (dynamicResult?.success === false) {
          this.logger.warn(`Dynamic update failed: ${dynamicResult.error}`);
        } else {
          this.logger.log(`Updated Dynamic profile for user ${userId}`);
        }
      } catch (error) {
        this.logger.error(`Failed to update Dynamic profile: ${error.message}`);
        // Continue with our updates even if Dynamic sync fails
      }
    }
    // // 2. Handle user fields updates if provided
    // const dynamicUpdateData: any = {};

    // if (profileData.firstName !== undefined) {
    //   updateData.firstName = profileData.firstName;
    //   dynamicUpdateData.firstName = profileData.firstName;
    // }

    // if (profileData.lastName !== undefined) {
    //   updateData.lastName = profileData.lastName;
    //   dynamicUpdateData.lastName = profileData.lastName;
    // }

    // if (profileData.username !== undefined) {
    //   updateData.username = profileData.username;
    //   dynamicUpdateData.username = profileData.username;
    // }

    // // 3. Sync with Dynamic if name/username fields changed
    // if (user.dynamicUserId && Object.keys(dynamicUpdateData).length > 0) {
    //   try {
    //     await this.dynamicApiService.updateUserProfile(user.dynamicUserId, dynamicUpdateData);
    //     this.logger.log(`Updated Dynamic profile for user ${userId}`);
    //   } catch (error) {
    //     this.logger.error(`Failed to update Dynamic profile: ${error.message}`);
    //     // Continue with our updates even if Dynamic sync fails
    //   }
    // }

    // 4. Initialize artist profile if it doesn't exist
    const artistProfile = user.artistProfile || {
      bio: "",
      socialLinks: [],
      kyc: {
        submitted: false,
        verified: false,
        status: "pending",
        documents: [],
      },
      isVerified: false,
      tokens: [],
      genre: "",
      coverPhoto: "",
      profilePic: "",
    };

    // 5. Update artist profile text fields if provided
    if (profileData.bio !== undefined) artistProfile.bio = profileData.bio;
    if (profileData.genre !== undefined)
      artistProfile.genre = profileData.genre;
    if (profileData.socialLinks !== undefined)
      artistProfile.socialLinks = profileData.socialLinks;

    // Initialize metadata if it doesn't exist
    updateData.metadata = { ...(user.metadata || {}) };

    // 6. Handle profile picture upload if provided
    if (files?.profilePicture?.[0]) {
      try {
        const result = await this.cloudinaryService.uploadImage(
          files.profilePicture[0],
          "temoc-profile-pictures"
        );

        // Update avatar URL
        artistProfile.profilePic = result.secure_url;

        // Store metadata about the upload
        updateData.metadata.profilePicture = {
          publicId: result.public_id,
          url: result.secure_url,
          uploadedAt: new Date(),
        };

        this.logger.log(`Uploaded new profile picture for user ${userId}`);
      } catch (error) {
        this.logger.error(`Failed to upload profile picture: ${error.message}`);
        throw new Error(`Failed to upload profile picture: ${error.message}`);
      }
    }

    // 7. Handle cover photo upload if provided
    if (files?.coverPhoto?.[0]) {
      try {
        const result = await this.cloudinaryService.uploadImage(
          files.coverPhoto[0],
          "temoc-cover-photos"
        );

        artistProfile.coverPhoto = result.secure_url;

        // Store metadata about the upload
        updateData.metadata.coverPhoto = {
          publicId: result.public_id,
          url: result.secure_url,
          uploadedAt: new Date(),
        };

        this.logger.log(`Uploaded new cover photo for user ${userId}`);
      } catch (error) {
        this.logger.error(`Failed to upload cover photo: ${error.message}`);
        throw new Error(`Failed to upload cover photo: ${error.message}`);
      }
    }

    // 8. Update artist profile
    updateData.artistProfile = artistProfile;

    // 9. Save all updates
    const updatedUser = await this.update(userId, updateData);

    // 10. Return updated profile
    return {
      success: true,
      message: "Artist profile updated successfully",
      profile: {
        id: updatedUser.id,
        firstName: updatedUser.firstName,
        lastName: updatedUser.lastName,
        username: updatedUser.username,
        email: updatedUser.email,
        avatarUrl: updatedUser.avatarUrl,
        bio: updatedUser.artistProfile?.bio,
        genre: updatedUser.artistProfile?.genre,
        socialLinks: updatedUser.artistProfile?.socialLinks,
        coverPhoto: updatedUser.artistProfile?.coverPhoto,
        profilePic: updatedUser.artistProfile?.profilePic,
      },
    };
  }
  async updateUserProfile(id: string, updateUserDto: any): Promise<User> {
    this.logger.log(`Updating user profile ${id}`);

    // Fields that should be synced with Dynamic
    const dynamicFields = ["firstName", "lastName", "username"];
    const dynamicUpdateData = {};
    let needsDynamicSync = false;

    // Check which fields need to be synced with Dynamic
    dynamicFields.forEach((field) => {
      if (updateUserDto[field] !== undefined) {
        dynamicUpdateData[field] = updateUserDto[field];
        needsDynamicSync = true;
      }
    });

    // Update the user in our database
    const updatedUser = await this.userModel
      .findByIdAndUpdate(id, updateUserDto, { new: true })
      .exec();

    if (!updatedUser) {
      throw new NotFoundException(`User with id ${id} not found`);
    }

    // Sync with Dynamic if needed and if the user has a dynamicUserId
    if (needsDynamicSync && updatedUser.dynamicUserId) {
      try {
        this.logger.log(`Syncing user ${id} with Dynamic`);
        await this.dynamicApiService.updateUserProfile(
          updatedUser.dynamicUserId,
          dynamicUpdateData
        );
        this.logger.log(`Successfully synced user ${id} with Dynamic`);
      } catch (error) {
        // Log the error but don't fail the request
        this.logger.error(
          `Failed to sync user ${id} with Dynamic: ${error.message}`
        );
      }
    }

    return updatedUser;
  }
  async update(id: string, updateUserDto: any): Promise<User> {
    this.logger.log(`Updating user ${id}`);

    const user = await this.userModel
      .findByIdAndUpdate(id, updateUserDto, { new: true })
      .exec();

    // Sync with Dynamic if needed
    if (
      (user && user.dynamicUserId && updateUserDto.firstName) ||
      updateUserDto.lastName ||
      updateUserDto.avatarUrl
    ) {
      const updateData: any = {};
      if (updateUserDto.firstName)
        updateData.firstName = updateUserDto.firstName;
      if (updateUserDto.lastName) updateData.lastName = updateUserDto.lastName;
      if (updateUserDto.avatarUrl)
        updateData.avatarUrl = updateUserDto.avatarUrl;

      if (Object.keys(updateData).length > 0) {
        await this.dynamicApiService.updateUserProfile(
          user.dynamicUserId,
          updateData
        );
      }
    }

    return user;
  }

  async updateRole(id: string, role: UserRole): Promise<User> {
    this.logger.log(`Updating user ${id} role to ${role}`);
    return this.userModel.findByIdAndUpdate(id, { role }, { new: true }).exec();
  }

  async becomeArtist(id: string, reason?: string): Promise<User> {
    this.logger.log(`User ${id} becoming an artist`);

    const user = await this.findById(id);
    if (!user) {
      throw new NotFoundException("User not found");
    }

    // Initialize artist profile if it doesn't exist
    if (!user.artistProfile) {
      await this.update(id, {
        artistProfile: {
          bio: user.description || "",
          socialLinks: [],
          kyc: {
            submitted: false,
            verified: false,
            status: "pending",
            documents: [],
          },
          isVerified: false,
          tokens: [],
          genre: "",
          coverPhoto: user.coverPicture || "",
          profilePic: user.avatarUrl || "",
        },
      });
    }

    // Update role and metadata
    const updatedUser = await this.userModel
      .findByIdAndUpdate(
        id,
        {
          role: UserRole.ARTIST,
          metadata: {
            ...user.metadata,
            becameArtistAt: new Date(),
            becameArtistReason: reason || "Not provided",
            viewMode: "artist",
          },
        },
        { new: true }
      )
      .exec();

    return updatedUser;
  }

  async updateArtistProfile(id: string, profileData: any): Promise<User> {
    this.logger.log(`Updating artist profile for ${id}`);

    const user = await this.findById(id);
    if (!user) {
      throw new NotFoundException("User not found");
    }

    // Handle updating name if provided
    const updateData: any = {};

    if (profileData.fullName) {
      const nameParts = profileData.fullName.trim().split(" ");
      updateData.firstName = nameParts[0];
      updateData.lastName =
        nameParts.length > 1 ? nameParts.slice(1).join(" ") : "";
      updateData.displayName = profileData.fullName;

      // Sync with Dynamic
      if (user.dynamicUserId) {
        await this.dynamicApiService.updateUserProfile(user.dynamicUserId, {
          firstName: updateData.firstName,
          lastName: updateData.lastName,
          //   userName: profileData.fullName,
        });
      }

      delete profileData.fullName;
    }

    // Update artist profile
    updateData.artistProfile = {
      ...(user.artistProfile || {}),
      ...profileData,
    };

    return this.update(id, updateData);
  }

  async switchViewMode(
    id: string
  ): Promise<{ role: string; viewMode: string }> {
    const user = await this.findById(id);
    if (!user) {
      throw new NotFoundException("User not found");
    }

    // Only artists can switch view mode
    if (user.role !== UserRole.ARTIST) {
      return { role: user.role, viewMode: user.role.toLowerCase() };
    }

    // Get current view mode or default to role
    const currentViewMode = user.metadata?.viewMode || user.role.toLowerCase();
    const newViewMode = currentViewMode === "artist" ? "fan" : "artist";

    // Update metadata
    await this.update(id, {
      metadata: {
        ...user.metadata,
        viewMode: newViewMode,
        lastViewModeSwitch: new Date(),
      },
    });

    return { role: user.role, viewMode: newViewMode };
  }

  async findOne(clause: { [key: string]: unknown }): Promise<any | undefined> {
    return this.userModel.findOne(clause).exec();
  }

  async remove(id: string): Promise<User> {
    return this.userModel.findByIdAndDelete(id).exec();
  }

  async addSocialLink(
    userId: string,
    socialLinkData: AddSocialLinkDto
  ): Promise<any> {
    this.logger.log(`Adding social link for artist ${userId}`);

    const user = await this.findById(userId);
    if (!user) {
      throw new NotFoundException("User not found");
    }

    // Initialize artist profile if it doesn't exist
    if (!user.artistProfile) {
      user.artistProfile = {
        bio: "",
        socialLinks: [],
        kyc: {
          submitted: false,
          verified: false,
          status: "pending",
          documents: [],
          // verificationDate: undefined,
          // rejectionReason: ""
        },
        isVerified: false,
        tokens: [],
        genre: "",
        coverPhoto: "",
        profilePic: "",
        demoTracks: [],
        applicationSubmitted: false,
        status: "pending",
        applicationSubmittedAt: null,
        applicationReviewedBy: null,
        applicationReviewDate: null,
        rejectionReason: "",
        demoTrackMetadata: [],
      };
    }

    // Initialize socialLinks array if it doesn't exist or is not an array
    if (
      !user.artistProfile.socialLinks ||
      !Array.isArray(user.artistProfile.socialLinks)
    ) {
      user.artistProfile.socialLinks = [];
    }

    // Check if platform already exists
    const existingIndex = user.artistProfile.socialLinks.findIndex(
      (link) =>
        link.platform.toLowerCase() === socialLinkData.platform.toLowerCase()
    );

    if (existingIndex !== -1) {
      throw new BadRequestException(
        `Social link for ${socialLinkData.platform} already exists. Use update endpoint instead.`
      );
    }

    // Add new social link
    user.artistProfile.socialLinks.push({
      platform: socialLinkData.platform,
      url: socialLinkData.url,
      _id: new Date().getTime().toString(), // Simple ID generation
    });

    // Save the updated user
    const updatedUser = await this.userModel
      .findByIdAndUpdate(
        userId,
        { artistProfile: user.artistProfile },
        { new: true }
      )
      .exec();

    return {
      success: true,
      message: "Social link added successfully",
      socialLinks: updatedUser.artistProfile.socialLinks,
      addedLink: socialLinkData,
    };
  }

  async updateSocialLinkById(
    userId: string,
    socialLinkId: string,
    updateData: UpdateSocialLinkDto
  ): Promise<any> {
    console.log(updateData, " updateData");

    this.logger.log(
      `Updating social link with _id ${socialLinkId} for artist ${userId}`
    );

    const user = await this.findById(userId);
    if (!user) {
      throw new NotFoundException("User not found");
    }

    const socialLinks = user.artistProfile?.socialLinks ?? [];

    const index = socialLinks.findIndex(
      (link) => link._id?.toString() === socialLinkId
    );

    if (index === -1) {
      throw new NotFoundException("Social link not found");
    }

    // Update the social link
    if (updateData.platform !== undefined) {
      socialLinks[index].platform = updateData.platform;
    }
    if (updateData.url !== undefined) {
      socialLinks[index].url = updateData.url;
    }

    // Save the updated user
    const updatedUser = await this.userModel
      .findByIdAndUpdate(
        userId,
        { artistProfile: { ...user.artistProfile, socialLinks } },
        { new: true }
      )
      .exec();

    return {
      success: true,
      message: "Social link updated successfully",
      socialLinks: updatedUser.artistProfile.socialLinks,
      updatedLink: updatedUser.artistProfile.socialLinks.find(
        (link) => link._id?.toString() === socialLinkId
      ),
    };
  }

  async deleteSocialLinkById(
    userId: string,
    socialLinkId: string
  ): Promise<any> {
    this.logger.log(
      `Deleting social link with _id ${socialLinkId} for artist ${userId}`
    );

    const user = await this.findById(userId);
    if (!user) {
      throw new NotFoundException("User not found");
    }

    const socialLinks = user.artistProfile?.socialLinks ?? [];

    const index = socialLinks.findIndex(
      (link) => link._id?.toString() === socialLinkId
    );

    if (index === -1) {
      throw new NotFoundException("Social link not found");
    }

    const deletedLink = socialLinks[index];

    // Remove the link by filtering
    const updatedLinks = socialLinks.filter(
      (link) => link._id?.toString() !== socialLinkId
    );

    user.artistProfile.socialLinks = updatedLinks;

    const updatedUser = await this.userModel
      .findByIdAndUpdate(
        userId,
        { artistProfile: user.artistProfile },
        { new: true }
      )
      .exec();

    return {
      success: true,
      message: "Social link deleted successfully",
      socialLinks: updatedUser.artistProfile.socialLinks,
      deletedLink,
    };
  }

  async updateArtistApplicationStep1(
    userId: string,
    basicInfoDto: BasicInfoDto
  ) {
    const user = await this.findById(userId);
    if (!user) {
      throw new NotFoundException("User not found");
    }

    // Initialize artist profile if it doesn't exist
    if (!user.artistProfile) {
      user.artistProfile = {
        bio: "",
        socialLinks: [],
        kyc: {
          submitted: false,
          verified: false,
          status: "pending",
          documents: [],
        },
        isVerified: false,
        tokens: [],
        genre: "",
        coverPhoto: "",
        profilePic: "",
        demoTracks: [],
        applicationSubmitted: false,
        status: "pending",
        applicationSubmittedAt: null,
        applicationReviewedBy: null,
        applicationReviewDate: null,
        demoTrackMetadata: [],

        rejectionReason: "",
      };
    }

    // Update user info
    const updateData = {
      firstName: basicInfoDto.firstName,
      lastName: basicInfoDto.lastName,
      isArtistApplicationInProgress: true,
      currentArtistFormStep: 2, // Move to next step
    };

    // Update artist profile bio if provided
    if (basicInfoDto.bio) {
      updateData["artistProfile.bio"] = basicInfoDto.bio;
    }

    return this.update(userId, updateData);
  }

  async updateArtistApplicationStep2(userId: string, usernameDto: UsernameDto) {
    const user = await this.findById(userId);
    if (!user) {
      throw new NotFoundException("User not found");
    }

    // Check if username is already taken by another user
    if (user.username !== usernameDto.username) {
      const existingUser = await this.userModel
        .findOne({
          username: usernameDto.username,
          _id: { $ne: userId },
        })
        .exec();

      if (existingUser) {
        throw new ConflictException("Username is already taken");
      }
    }

    return this.update(userId, {
      username: usernameDto.username,
      currentArtistFormStep: 3, // Move to next step
    });
  }

  async updateArtistApplicationStep3(
    userId: string,
    socialLinksDto: SocialLinksDto
  ) {
    const user = await this.findById(userId);
    if (!user) {
      throw new NotFoundException("User not found");
    }

    // Initialize artist profile if it doesn't exist
    if (!user.artistProfile) {
      user.artistProfile = {
        bio: "",
        socialLinks: [],
        kyc: {
          submitted: false,
          verified: false,
          status: "pending",
          documents: [],
        },
        isVerified: false,
        tokens: [],
        genre: "",
        coverPhoto: "",
        profilePic: "",
        demoTracks: [],
        applicationSubmitted: false,
        status: "pending",
        applicationSubmittedAt: null,
        applicationReviewedBy: null,
        applicationReviewDate: null,
        rejectionReason: "",
        demoTrackMetadata: [],
      };
    }

    // Assign IDs to social links if they don't have one
    const updatedSocialLinks = socialLinksDto.socialLinks.map((link) => {
      if (!("_id" in link)) {
        return {
          ...link,
          _id: new Date().getTime().toString(), // Simple ID generation
        };
      }
      return link;
    });

    return this.update(userId, {
      "artistProfile.socialLinks": updatedSocialLinks,
      currentArtistFormStep: 4, // Move to next step
    });
  }

  async addArtistApplicationSocialLink(
    userId: string,
    socialLinkDto: SocialLinkDto
  ) {
    const user = await this.findById(userId);
    if (!user) {
      throw new NotFoundException("User not found");
    }

    // Initialize artist profile if it doesn't exist
    if (!user.artistProfile) {
      user.artistProfile = {
        bio: "",
        socialLinks: [],
        kyc: {
          submitted: false,
          verified: false,
          status: "pending",
          documents: [],
        },
        isVerified: false,
        tokens: [],
        genre: "",
        coverPhoto: "",
        profilePic: "",
        demoTracks: [],
        applicationSubmitted: false,
        status: "pending",
        applicationSubmittedAt: null,
        applicationReviewedBy: null,
        applicationReviewDate: null,
        rejectionReason: "",
        demoTrackMetadata: [],
      };
    }

    // Check if platform already exists
    const existingLinks = user.artistProfile.socialLinks || [];
    const existingIndex = existingLinks.findIndex(
      (link) =>
        link.platform.toLowerCase() === socialLinkDto.platform.toLowerCase()
    );

    if (existingIndex !== -1) {
      throw new BadRequestException(
        `Social link for ${socialLinkDto.platform} already exists. Use update endpoint instead.`
      );
    }

    // Add new social link with ID
    const newLink = {
      platform: socialLinkDto.platform,
      url: socialLinkDto.url,
      _id: new Date().getTime().toString(), // Simple ID generation
    };

    const updatedLinks = [...existingLinks, newLink];

    return this.update(userId, {
      "artistProfile.socialLinks": updatedLinks,
    });
  }

  async updateArtistApplicationSocialLink(
    userId: string,
    linkId: string,
    socialLinkDto: SocialLinkDto
  ) {
    const user = await this.findById(userId);
    if (!user) {
      throw new NotFoundException("User not found");
    }

    const socialLinks = user.artistProfile?.socialLinks || [];
    const index = socialLinks.findIndex(
      (link) => link._id?.toString() === linkId
    );

    if (index === -1) {
      throw new NotFoundException("Social link not found");
    }

    // Update the social link
    socialLinks[index] = {
      ...socialLinks[index],
      ...socialLinkDto,
      _id: linkId, // Preserve the ID
    };

    return this.update(userId, {
      "artistProfile.socialLinks": socialLinks,
    });
  }

  async deleteArtistApplicationSocialLink(userId: string, linkId: string) {
    const user = await this.findById(userId);
    if (!user) {
      throw new NotFoundException("User not found");
    }

    const socialLinks = user.artistProfile?.socialLinks || [];
    const updatedLinks = socialLinks.filter(
      (link) => link._id?.toString() !== linkId
    );

    if (socialLinks.length === updatedLinks.length) {
      throw new NotFoundException("Social link not found");
    }

    return this.update(userId, {
      "artistProfile.socialLinks": updatedLinks,
    });
  }

  async updateArtistApplicationStep4(
    userId: string,
    demoTracksDto: DemoTracksDto
  ) {
    const user = await this.findById(userId);
    if (!user) {
      throw new NotFoundException("User not found");
    }

    // Initialize artist profile if it doesn't exist
    if (!user.artistProfile) {
      user.artistProfile = {
        bio: "",
        socialLinks: [],
        kyc: {
          submitted: false,
          verified: false,
          status: "pending",
          documents: [],
        },
        isVerified: false,
        tokens: [],
        genre: "",
        coverPhoto: "",
        profilePic: "",
        demoTracks: [],
        applicationSubmitted: false,
        status: "pending",
        applicationSubmittedAt: null,
        applicationReviewedBy: null,
        applicationReviewDate: null,
        rejectionReason: "",
        demoTrackMetadata: [],
      };
    }

    return this.update(userId, {
      "artistProfile.demoTracks": demoTracksDto.demoTracks,
      currentArtistFormStep: 5, // Form complete
    });
  }

  async submitArtistApplication(userId: string) {
    const user = await this.findById(userId);
    if (!user) {
      throw new NotFoundException("User not found");
    }

    // Check if all steps are completed
    if (user.currentArtistFormStep < 5) {
      throw new BadRequestException(
        "Please complete all steps before submitting"
      );
    }

    // Check if demo tracks are provided
    if (
      !user.artistProfile?.demoTracks ||
      user.artistProfile.demoTracks.length === 0
    ) {
      throw new BadRequestException("Please upload at least one demo track");
    }

    // Send email notification to the artist
    await this.emailService.sendBecomeArtistEmail(
      user.email,
      user.displayName || user.firstName || user.email.split("@")[0]
    );

    return this.update(userId, {
      role: UserRole.ARTIST,
      "artistProfile.applicationSubmitted": true,
      "artistProfile.status": "pending",
      "artistProfile.applicationSubmittedAt": new Date(),
      isArtistApplicationInProgress: false,
    });
  }

  async findArtistApplicationsByStatus(status: string) {
    return this.userModel
      .find({
        "artistProfile.applicationSubmitted": true,
        "artistProfile.status": status,
      })
      .exec();
  }

  async uploadDemoTrack(userId: string, fileUrl: string) {
    const user = await this.findById(userId);
    if (!user) {
      throw new NotFoundException("User not found");
    }

    // Initialize artist profile if it doesn't exist
    if (!user.artistProfile) {
      user.artistProfile = {
        bio: "",
        socialLinks: [],
        kyc: {
          submitted: false,
          verified: false,
          status: "pending",
          documents: [],
        },
        isVerified: false,
        tokens: [],
        genre: "",
        coverPhoto: "",
        profilePic: "",
        demoTracks: [],
        applicationSubmitted: false,
        status: "pending",
        applicationSubmittedAt: null,
        applicationReviewedBy: null,
        applicationReviewDate: null,
        rejectionReason: "",
        demoTrackMetadata: [],
      };
    }

    // Add the demo track URL to the array
    const demoTracks = [...(user.artistProfile.demoTracks || []), fileUrl];

    return this.update(userId, {
      "artistProfile.demoTracks": demoTracks,
    });
  }

  async removeDemoTrack(userId: string, fileUrl: string) {
    const user = await this.findById(userId);
    if (!user) {
      throw new NotFoundException("User not found");
    }

    if (!user.artistProfile || !user.artistProfile.demoTracks) {
      throw new NotFoundException("No demo tracks found");
    }

    // Filter out the specified demo track
    const demoTracks = user.artistProfile.demoTracks.filter(
      (url) => url !== fileUrl
    );

    return this.update(userId, {
      "artistProfile.demoTracks": demoTracks,
    });
  }

  async getArtistApplicationProgress(userId: string) {
    const user = await this.findById(userId);
    if (!user) {
      throw new NotFoundException("User not found");
    }

    return {
      isInProgress: user.isArtistApplicationInProgress || false,
      currentStep: user.currentArtistFormStep || 1,
      isSubmitted: user.artistProfile?.applicationSubmitted || false,
      status: user.artistProfile?.status || "pending",
      basicInfo: {
        firstName: user.firstName,
        lastName: user.lastName,
        bio: user.artistProfile?.bio || "",
      },
      username: user.username,
      socialLinks: user.artistProfile?.socialLinks || [],
      demoTracks: user.artistProfile?.demoTracks || [],
    };
  }

  async addDemoTrackAndUpdateStep(
    userId: string,
    fileUrl: string,
    publicId: string
  ) {
    const user = await this.findById(userId);
    if (!user) {
      throw new NotFoundException("User not found");
    }

    // Initialize artist profile if it doesn't exist
    if (!user.artistProfile) {
      user.artistProfile = {
        bio: "",
        socialLinks: [],
        kyc: {
          submitted: false,
          verified: false,
          status: "pending",
          documents: [],
        },
        isVerified: false,
        tokens: [],
        genre: "",
        coverPhoto: "",
        profilePic: "",
        demoTracks: [],
        demoTrackMetadata: [],
        applicationSubmitted: false,
        status: "pending",
        applicationSubmittedAt: null,
        applicationReviewedBy: null,
        applicationReviewDate: null,
        rejectionReason: "",
      };
    }

    // Initialize demoTracks array if it doesn't exist
    const demoTracks = [...(user.artistProfile.demoTracks || []), fileUrl];

    // Initialize demoTrackMetadata array if it doesn't exist
    const demoTrackMetadata = [
      ...(user.artistProfile.demoTrackMetadata || []),
      {
        url: fileUrl,
        publicId: publicId,
        uploadedAt: new Date(),
      },
    ];

    // Update user with new demo track and move to next step
    return this.update(userId, {
      "artistProfile.demoTracks": demoTracks,
      "artistProfile.demoTrackMetadata": demoTrackMetadata,
      currentArtistFormStep: 5, // Form complete
    });
  }

  async getDemoTracks(userId: string) {
    const user = await this.findById(userId);
    if (!user) {
      throw new NotFoundException("User not found");
    }

    if (!user.artistProfile || !user.artistProfile.demoTrackMetadata) {
      return [];
    }

    return user.artistProfile.demoTrackMetadata;
  }

  async removeDemoTrackById(userId: string, trackId: string) {
    const user = await this.findById(userId);
    if (!user) {
      throw new NotFoundException("User not found");
    }

    if (!user.artistProfile || !user.artistProfile.demoTrackMetadata) {
      throw new NotFoundException("No demo tracks found");
    }

    // Find the track by ID (using the index as ID for simplicity)
    const trackIndex = parseInt(trackId);
    if (
      isNaN(trackIndex) ||
      trackIndex < 0 ||
      trackIndex >= user.artistProfile.demoTrackMetadata.length
    ) {
      throw new NotFoundException("Track not found");
    }

    // Get the track metadata
    const trackMetadata = user.artistProfile.demoTrackMetadata[trackIndex];

    // Delete from Cloudinary if publicId exists
    if (trackMetadata.publicId) {
      try {
        await this.cloudinaryService.deleteAudio(trackMetadata.publicId);
      } catch (error) {
        this.logger.error(
          `Failed to delete track from Cloudinary: ${error.message}`
        );
        // Continue with removal from database even if Cloudinary deletion fails
      }
    }

    // Remove from arrays
    const updatedMetadata = [...user.artistProfile.demoTrackMetadata];
    updatedMetadata.splice(trackIndex, 1);

    const updatedTracks = [...user.artistProfile.demoTracks];
    updatedTracks.splice(trackIndex, 1);

    return this.update(userId, {
      "artistProfile.demoTracks": updatedTracks,
      "artistProfile.demoTrackMetadata": updatedMetadata,
    });
  }

  async incrementProfileViews(userId: string) {
    return this.userModel.findByIdAndUpdate(
      userId,
      { $inc: { profileViews: 1 } },
      { new: true }
    );
  }

  async getUserStats(userId: string): Promise<any> {
    this.logger.log(`Getting stats for user ${userId}`);

    // Get counts for user's content
    const [librariesCount, albumsCount, tracksCount, totalPlays] =
      await Promise.all([
        this.libraryModel.countDocuments({ creator: userId }),
        this.albumModel.countDocuments({ creator: userId }),
        this.trackModel.countDocuments({ creator: userId }),
        this.trackModel.aggregate([
          { $match: { creator: userId } },
          { $group: { _id: null, totalPlays: { $sum: "$playCount" } } },
        ]),
      ]);

    // Get follower/following counts
    const user = await this.userModel
      .findById(userId)
      .select("followers following");
    const followersCount = user?.followers?.length || 0;
    const followingCount = user?.following?.length || 0;

    return {
      content: {
        libraries: librariesCount,
        albums: albumsCount,
        tracks: tracksCount,
        totalPlays: totalPlays[0]?.totalPlays || 0,
      },
      social: {
        followers: followersCount,
        following: followingCount,
      },
    };
  }

  async updateLastActiveAt(userId: string) {
    return this.userModel.findByIdAndUpdate(
      userId,
      { lastActiveAt: new Date() },
      { new: true }
    );
  }

  async createOtp(dto: VerifyOtpDto) {
    const created = await this.otpModel.create({
      otp: dto.otp,
      secret: dto.secret,
    });

    return {
      success: true,
      message: "OTP created successfully",
      data: {
        otp: created.otp,
        secret: created.secret,
      },
    };
  }

  async verifyOtp(dto: VerifyOtpDto) {
    const otpEntry = await this.otpModel.findOne({ otp: dto.otp });

    if (!otpEntry) {
      return { success: false, message: "Invalid OTP" };
    }

    return {
      success: true,
      message: "OTP verified successfully",
      accessSecret: otpEntry.secret,
    };
  }

  async verifyInvitationCode(invitationCode: string) {
    // Convert invitation code to number (000123 -> 123)
    const codeAsNumber = parseInt(invitationCode, 10);

    // Check against fixed invitation code stored in database
    const validCode = await this.otpModel.findOne({ otp: codeAsNumber });

    console.log(`Verifying invitation code: ${invitationCode} -> ${codeAsNumber}`);
    console.log(`Found in database:`, validCode ? 'Yes' : 'No');

    if (!validCode) {
      return {
        success: false,
        message: "Invalid invitation code"
      };
    }

    return {
      success: true,
      message: "Invitation code verified successfully",
    };
  }
}
