// src/token/schemas/token.schema.ts
import { <PERSON><PERSON>, <PERSON>hem<PERSON>, SchemaFactory } from "@nestjs/mongoose";
import { Document } from "mongoose";

export type OtpDocument = Otp & Document;

@Schema({ timestamps: true })
export class Otp extends Document {
  @Prop({ required: true })
  otp: number;

  @Prop()
  secret: string;
}

export const OtpSchema = SchemaFactory.createForClass(Otp);
