# Invitation Code Setup

This document explains how to set up the invitation code system for the Temoc music platform.

## Overview

The website is now protected by an invitation code system. Users must enter the correct 6-digit invitation code to access the website.

## Setup Instructions

### 1. Add Invitation Code to Database

Run the seeding script to add the invitation code to your database:

```bash
cd temoc-be
npm run seed:invitation
```

This will add the invitation code `000123` to your database.

### 2. Manual Database Setup (Alternative)

If you prefer to add the invitation code manually, connect to your MongoDB database and insert:

```javascript
db.otps.insertOne({
  otp: 123,
  secret: "invitation-access-key",
  createdAt: new Date(),
  updatedAt: new Date()
})
```

### 3. Verify Setup

1. Start your backend server:
   ```bash
   cd temoc-be
   npm run start:dev
   ```

2. Start your frontend:
   ```bash
   cd temoc-fe
   npm run dev
   ```

3. Visit the website - you should see the invitation code screen
4. Enter `000123` to access the website

## How It Works

1. **Frontend**: The layout checks if the user has verified the invitation code
2. **Verification Screen**: Shows when user hasn't entered the code
3. **API Call**: Verifies the entered code against the database
4. **Local Storage**: Remembers verification status to avoid repeated prompts
5. **Access Granted**: User can access the full website after verification

## Changing the Invitation Code

To change the invitation code:

1. Update the database record:
   ```javascript
   db.otps.updateOne(
     { otp: 123 },
     { $set: { otp: YOUR_NEW_CODE } }
   )
   ```

2. Or delete the old one and create a new one:
   ```javascript
   db.otps.deleteOne({ otp: 123 })
   db.otps.insertOne({
     otp: YOUR_NEW_CODE,
     secret: "invitation-access-key",
     createdAt: new Date(),
     updatedAt: new Date()
   })
   ```

## Security Notes

- The invitation code is stored as a number in the database
- Users enter it as a 6-digit string (e.g., "000123")
- Verification status is stored in localStorage
- Users need to re-verify if they clear their browser data

## API Endpoints

- `POST /users/verify-invitation` - Verifies the invitation code
- Body: `{ "invitationCode": "000123" }`
- Response: `{ "success": true, "message": "Invitation code verified successfully" }`
