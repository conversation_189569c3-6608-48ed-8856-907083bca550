@import 'tailwindcss/base';
@import './components/Typo.css';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

@layer base {
  p {
    @apply text-sm sm:text-base;
  }
  h2 {
    @apply font-DreamAvenue !text-[38px] !font-normal text-[#292D32] md:!text-[80px];
  }
}

h1,
h2,
h3,
h4,
h5,
h6 {
  line-height: 1 !important;
}
.heading {
  font-size: 20px;
}
.bd {
  border: 1px solid red;
}
.AtSocialSlider1 .swiper-pagination-bullet {
  border: 1px solid #ff8000 !important;
  height: 8px !important;
  background-color: transparent !important;
  width: 8px !important;
  transition: all 0.3s;
  opacity: 1;
}
.AtSocialSlider1 .swiper-pagination-bullet-active {
  background-color: #ff8000 !important;
  opacity: 1;
  width: 30px !important; /* Expanded width */
  height: 8px !important;
  border-radius: 24px !important;
  margin-right: -5px;
}
.AtSocialSlider1 .swiper-pagination {
  position: relative !important;
  margin-top: 50px !important;
}

.hideScrollbar::-webkit-scrollbar {
  width: 0 !important;
  height: 0 !important;
}

.hideScrollbar {
  scrollbar-width: none !important;
}

.dynamic-shadow-dom {
  --dynamic-connect-button-background: #ff6e00;
  --dynamic-connect-button-color: white;
  --dynamic-connect-button-border: 1px solid #ff6e00;
  --dynamic-connect-button-shadow: none;
  --dynamic-connect-button-background-hover: #ff6e00;
  --dynamic-connect-button-color-hover: white;
  --dynamic-connect-button-border-hover: 1px solid #ff6e00;
  --dynamic-connect-button-shadow-hover: none;
  --dynamic-tooltip-color: #000;
  --dynamic-tooltip-text-color: #fff;
  --dynamic-connect-button-radius: 5rem;
}

.AtSocialSliderSignup .swiper-pagination-bullet {
  border: 2px solid white !important;
  height: 10px !important;
  background-color: transparent !important;
  width: 10px !important;
  transition: all 0.3s;
  opacity: 1;
}
.AtSocialSliderSignup .swiper-pagination-bullet-active {
  background-color: white !important;
  opacity: 1;
  width: 20px !important; /* Expanded width */
  height: 10px !important;
  border-radius: 24px !important;
  margin-right: -5px;
}
.AtSocialSliderSignup .swiper-pagination {
  /* position: relative !important; */
  /* margin-top: 50px !important; */
}
.Atbg {
  background-size: 100% 100%;
}
input[type='number']::-webkit-outer-spin-button,
input[type='number']::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0; /* Optional: Adjust margins */
}
.shadow {
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.1);
}
.no-scroll {
  overflow: hidden;
}
.bgBlur {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

/* Enhanced Music Player Backgrounds */
.music-player-bg {
  background: linear-gradient(
    135deg,
    #1a1a1a 0%,
    #2d2d2d 25%,
    #1a1a1a 50%,
    #0f0f0f 100%
  );
  position: relative;
}

.music-player-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(
      circle at 20% 80%,
      rgba(255, 107, 0, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 20%,
      rgba(255, 140, 0, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 40% 40%,
      rgba(229, 90, 0, 0.05) 0%,
      transparent 50%
    );
  pointer-events: none;
}

.music-gradient-overlay {
  background: linear-gradient(
    135deg,
    rgba(255, 107, 0, 0.8) 0%,
    rgba(255, 140, 0, 0.6) 30%,
    rgba(229, 90, 0, 0.4) 70%,
    rgba(0, 0, 0, 0.8) 100%
  );
}

.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.music-card-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.music-card-hover:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Custom scrollbar for music components */
.music-scroll::-webkit-scrollbar {
  width: 6px;
}

.music-scroll::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.music-scroll::-webkit-scrollbar-thumb {
  background: rgba(255, 107, 0, 0.6);
  border-radius: 3px;
}

.music-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 107, 0, 0.8);
}

/*  */

.chakra-avatar__excess {
  z-index: 100 !important;
  height: 40px;
  width: 40px;
  background-color: gainsboro;
  border-radius: 50%;
}
