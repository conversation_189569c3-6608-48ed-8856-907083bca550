'use client';
import React, { useState, useEffect } from 'react';
import { useMusicPlayer } from '@/context/MusicPlayerContext';
import { FaPlay, FaPause, FaArrowLeft, FaShare } from 'react-icons/fa';
import { IoAdd, IoRepeat, IoShuffle } from 'react-icons/io5';
import ImageComponent from '@/components/common/ImageComponent';
import { generateGradientFromImage } from '@/utils/colorExtractor';
import { useQuery } from '@tanstack/react-query';
import { discoveryApi } from '@/services/discovery';
import { contentApi } from '@/services/content';
import Skeleton from 'react-loading-skeleton';
import { useFollowSync } from '@/hooks/useFollowSync';
import { FollowButton } from '@/components/social/FollowButton';
import { useRouter } from 'next/navigation';
import Loader from '@/components/common/Loader';
import CardSkeleton from '@/components/ui/Skelton/CardSkelton';
import CommonSlider from '@/components/common/CommonSlider';

interface TrackPageProps {
  params: {
    id: string;
  };
}

// Artist sidebar item component
const ArtistSidebarItem = ({ artist }: { artist: any }) => {
  const { isFollowing, isLoading, handleFollowToggle, loadingAction } =
    useFollowSync({
      userId: artist._id,
      displayName: artist.displayName,
      initialFollowStatus: artist.isFollowing,
    });

  return (
    // <div className="flex items-center space-x-3 rounded-lg p-2 transition-colors hover:bg-gray-700">
    //   <div className="h-10 w-10 overflow-hidden rounded-full">
    //     <ImageComponent
    //       src={
    //         artist.artistProfile?.profilePic ||
    //         artist.avatarUrl ||
    //         '/assets/images/women.avif'
    //       }
    //       alt={artist.displayName}
    //       className="h-full w-full object-cover"
    //       fill
    //     />
    //   </div>
    //   <div className="min-w-0 flex-1">
    //     <p className="truncate text-sm font-medium text-white">
    //       {artist.displayName}
    //     </p>
    //     <p className="truncate text-xs text-gray-400">
    //       {artist.followersCount || 0} followers
    //     </p>
    //   </div>
    //   <FollowButton
    //     isFollowing={isFollowing}
    //     isLoading={isLoading}
    //     onClick={handleFollowToggle}
    //     loadingAction={loadingAction || undefined}
    //     size="sm"
    //     className="!px-2 !py-1 !text-xs"
    //   />
    // </div>
    <div className="group relative w-full rounded-[10px] bg-white p-2 shadow-[0_5px_10px_rgba(0,0,0,0.05)] transition-all duration-300 xl:p-4">
      <div className="h-[190px] w-full overflow-hidden rounded-md">
        <img
          src={
            artist.artistProfile?.profilePic ||
            artist.avatarUrl ||
            '/assets/images/women.avif'
          }
          className="h-full w-full object-cover transition-transform duration-300 group-hover:scale-110"
        />
      </div>
      <div className="min-w-0 flex-1">
        <div className="mt-3 flex items-center justify-between">
          <p className="truncate text-sm font-medium">{artist.displayName}</p>
          <p className="truncate text-xs">
            {artist.followersCount || 0} followers
          </p>
        </div>
      </div>
      <FollowButton
        isFollowing={isFollowing}
        isLoading={isLoading}
        onClick={handleFollowToggle}
        loadingAction={loadingAction || undefined}
        size="sm"
        className="!mt-4 !w-full !px-2 !py-2 !text-xs"
      />
    </div>
  );
};

const TrackPage = ({ params }: TrackPageProps) => {
  const router = useRouter();
  const {
    playTrack,
    pauseTrack,
    resumeTrack,
    addToQueue,
    nextTrack,
    previousTrack,
    toggleShuffle,
    toggleLoop,
    isShuffled,
    isLooping,
    currentTrack,
    isPlaying,
    currentTime,
    duration,
  } = useMusicPlayer();

  const [gradientBackground, setGradientBackground] = useState('');
  const [trackData, setTrackData] = useState<any>(null);

  // Fetch track data by ID
  const { data: track, isLoading: trackLoading } = useQuery({
    queryKey: ['track', params.id],
    queryFn: async () => {
      // For now, we'll get the track from trending songs
      // In a real app, you'd have a specific API endpoint for getting track by ID
      const trendingSongs = await contentApi.getTrendingSongs();
      return trendingSongs.find((song: any) => song._id === params.id);
    },
  });

  // Use the existing follow sync hook
  const {
    isFollowing,
    isLoading: isFollowLoading,
    handleFollowToggle,
    loadingAction,
  } = useFollowSync({
    userId: track?.creator?._id || '',
    displayName: track?.creator?.displayName || track?.artist,
    initialFollowStatus: undefined, // Let the hook fetch the status
  });

  // Fetch popular artists for sidebar
  const { data: popularArtists, isLoading: artistsLoading } = useQuery({
    queryKey: ['popular-artists-track-page'],
    queryFn: () => discoveryApi.getPopularArtists(8),
  });

  // Fetch recommended tracks
  const { data: recommendedTracks, isLoading: tracksLoading } = useQuery({
    queryKey: ['recommended-tracks'],
    queryFn: () => contentApi.getTrendingSongs(),
  });

  // Set track data when loaded
  useEffect(() => {
    if (track) {
      setTrackData(track);
    }
  }, [track]);

  // Generate gradient from track thumbnail
  useEffect(() => {
    if (trackData?.thumbnailUrl) {
      generateGradientFromImage(trackData.thumbnailUrl).then((gradient) => {
        setGradientBackground(gradient);
      });
    }
  }, [trackData]);

  // Auto-play the track when loaded
  useEffect(() => {
    if (trackData && recommendedTracks) {
      // Only auto-play if this track is not already playing
      if (!currentTrack || currentTrack._id !== trackData._id) {
        // @ts-ignore

        playTrack(trackData, recommendedTracks);
      }
    }
  }, [trackData, recommendedTracks, currentTrack, playTrack]);

  if (trackLoading || !trackData) {
    return (
      <div className="flex h-screen items-center justify-center bg-[#F8F8F8]">
        <Loader />
      </div>
    );
  }

  const formatTime = (time: number): string => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const handlePlayPause = () => {
    if (isPlaying && currentTrack?._id === trackData._id) {
      pauseTrack();
    } else if (currentTrack?._id === trackData._id) {
      resumeTrack();
    } else {
      // @ts-ignore

      playTrack(trackData, recommendedTracks || []);
    }
  };

  const progressPercentage = duration ? (currentTime / duration) * 100 : 0;
  const isCurrentTrack = currentTrack?._id === trackData._id;

  return (
    <div className="min-h-screen rounded-bl-lg rounded-tl-lg bg-[#F8F8F8]">
      {/* Header */}
      <div className="flex h-16 items-center justify-between px-6">
        <button
          onClick={() => router.back()}
          className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-700 text-white transition-colors"
        >
          <FaArrowLeft className="text-sm" />
        </button>
        <h1 className="text-lg font-semibold">Now Playing</h1>
        <div className="w-8" /> {/* Spacer */}
      </div>

      <div className="">
        {/* Main Content */}
        <div className="flex-1 overflow-y-auto">
          {/* Track Header with Gradient */}
          <div
            className="relative h-64 p-4"
            style={{
              background:
                gradientBackground ||
                'linear-gradient(135deg, #FF6B00 0%, #FF8C00 50%, #E55A00 100%)',
            }}
          >
            <div className="flex h-full items-center space-x-6 xs:space-x-4">
              <div className="flex-shrink-0 overflow-hidden rounded-lg shadow-2xl">
                <ImageComponent
                  src={trackData?.thumbnailUrl || '/assets/images/women.avif'}
                  alt={trackData?.title || 'Track'}
                  className="object-cover"
                  fill
                  figClassName="sm:h-48  sm:w-48 h-36 w-36 xs:h-24 xs:w-24"
                />
              </div>

              {/* Track Details */}
              <div className="min-w-0 flex-1">
                <p className="text-sm font-medium text-white/80 xs:text-xs">
                  TRACK
                </p>
                <h1 className="mt-2 max-w-[550px] truncate text-3xl font-bold text-white md:text-4xl lg:max-w-[670px] xs:text-2xl">
                  {trackData?.title || 'Unknown Track'}
                </h1>
                <div className="mt-4 flex items-center space-x-2 text-white/90">
                  <div className="overflow-hidden rounded-full">
                    <ImageComponent
                      src={
                        trackData?.creator?.avatarUrl ||
                        '/assets/images/women.avif'
                      }
                      alt={trackData?.creator?.displayName || 'Artist'}
                      className="h-full w-full rounded-full object-cover"
                      figClassName="h-6 w-6 rounded-full"
                      fill
                    />
                  </div>
                  <span className="text-sm font-medium xs:text-xs">
                    {trackData?.artist ||
                      trackData?.creator?.displayName ||
                      'Unknown Artist'}
                  </span>
                  <span>•</span>
                  <span className="text-sm xs:text-xs">
                    {trackData?.playCount || 0} plays
                  </span>
                  <span>•</span>
                  <span className="text-sm xs:text-xs">
                    {formatTime(trackData?.duration || 0)}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Player Controls */}
          <div className="bg-[#F8F8F8] p-4">
            <div className="flex items-center justify-between">
              {/* Left Controls */}
              <div className="flex items-center space-x-4">
                <button
                  onClick={previousTrack}
                  className="p-2 text-gray-400 transition-colors hover:text-white"
                >
                  <FaArrowLeft className="text-sm" />
                </button>

                <button
                  onClick={handlePlayPause}
                  className="flex h-12 w-12 items-center justify-center rounded-full bg-primary text-white transition-transform hover:scale-105"
                >
                  {isPlaying && isCurrentTrack ? (
                    <FaPause className="text-lg" />
                  ) : (
                    <FaPlay className="ml-1 text-lg" />
                  )}
                </button>

                <button
                  onClick={nextTrack}
                  className="p-2 text-gray-400 transition-colors hover:text-white"
                >
                  <FaArrowLeft className="rotate-180 text-sm" />
                </button>

                <button
                  onClick={toggleShuffle}
                  className={`p-2 transition-colors ${
                    isShuffled
                      ? 'text-primary'
                      : 'text-gray-400 hover:text-white'
                  }`}
                >
                  <IoShuffle className="text-sm" />
                </button>

                <button
                  onClick={toggleLoop}
                  className={`p-2 transition-colors ${
                    isLooping
                      ? 'text-primary'
                      : 'text-gray-400 hover:text-white'
                  }`}
                >
                  <IoRepeat className="text-sm" />
                </button>
              </div>

              {/* Right Actions */}
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => addToQueue(trackData)}
                  className="p-2 text-gray-400 transition-colors hover:text-white"
                >
                  <IoAdd className="text-lg" />
                </button>

                <button className="p-2 text-gray-400 transition-colors hover:text-white">
                  <FaShare className="text-lg" />
                </button>
              </div>
            </div>

            {/* Progress Bar */}
            {isCurrentTrack && (
              <div className="mt-4">
                <div className="relative h-2 w-full overflow-hidden rounded-full bg-gray-300">
                  <div
                    className="absolute h-full rounded-full bg-primary transition-all"
                    style={{ width: `${progressPercentage}%` }}
                  />
                </div>
                <div className="mt-2 flex justify-between text-sm text-gray-400">
                  <span>{formatTime(currentTime)}</span>
                  <span>{formatTime(duration)}</span>
                </div>
              </div>
            )}
          </div>

          {/* Main Content Area */}
          <div className="flex-1 p-4">
            {/* Artist Info */}
            <div className="mb-8">
              <h3 className="mb-4 text-lg font-semibold">Artist</h3>
              <div className="flex items-center space-x-4 rounded-lg bg-white p-4">
                <div className="overflow-hidden rounded-full">
                  <ImageComponent
                    src={
                      trackData?.creator?.avatarUrl ||
                      '/assets/images/women.avif'
                    }
                    alt={trackData?.creator?.displayName || 'Artist'}
                    className="h-full w-full object-cover"
                    figClassName="h-16 w-16"
                    fill
                  />
                </div>
                <div className="flex-1">
                  <p className="font-medium">
                    {trackData?.creator?.displayName || 'Unknown Artist'}
                  </p>
                  <p className="text-sm text-gray-400">
                    @{trackData?.creator?.username || 'unknown'}
                  </p>
                </div>
                <FollowButton
                  isFollowing={isFollowing}
                  isLoading={isFollowLoading}
                  onClick={handleFollowToggle}
                  loadingAction={loadingAction || undefined}
                  size="md"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Sidebar */}
      </div>
      <div className="bg-[#F8F8F8] p-4">
        {/* Popular Artists */}
        <div className="mb-8">
          <h3 className="mb-4 text-lg font-semibold">Popular Artists</h3>
          {/* 
          <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-5 lg:grid-cols-7">
            {artistsLoading
              ? Array.from({ length: 5 }).map((_, i) => (
                  <>
                    <CardSkeleton />
                    <CardSkeleton />
                    <CardSkeleton />
                    <CardSkeleton />
                    <CardSkeleton />
                    <CardSkeleton />
                    <CardSkeleton />
                  </>
                ))
              : popularArtists
                  ?.slice(0, 5)
                  .map((artist: any) => (
                    <ArtistSidebarItem key={artist._id} artist={artist} />
                  ))}
          </div> */}
          {artistsLoading ? (
            <CommonSlider delay={true}>
              {Array.from({ length: 5 }).map((_, i) => (
                <div key={i} className="mb-3">
                  <CardSkeleton />
                </div>
              ))}
            </CommonSlider>
          ) : (
            popularArtists &&
            popularArtists.length > 0 && (
              <CommonSlider delay={true}>
                {popularArtists?.slice(0, 5).map((artist: any) => (
                  <div key={artist._id} className="mb-3">
                    <ArtistSidebarItem artist={artist} />
                  </div>
                ))}
              </CommonSlider>
            )
          )}
        </div>

        {/* Recommended Tracks */}
        <div>
          <h3 className="mb-4 text-lg font-semibold">Recommended Songs</h3>
          <div className="space-y-3">
            {tracksLoading
              ? Array.from({ length: 6 }).map((_, i) => (
                  <div key={i} className="flex items-center space-x-3">
                    <Skeleton
                      height={40}
                      width={40}
                      baseColor="#374151"
                      highlightColor="#4B5563"
                    />
                    <div className="flex-1">
                      <Skeleton
                        height={14}
                        width="70%"
                        baseColor="#374151"
                        highlightColor="#4B5563"
                      />
                      <Skeleton
                        height={12}
                        width="50%"
                        className="mt-1"
                        baseColor="#374151"
                        highlightColor="#4B5563"
                      />
                    </div>
                    <Skeleton
                      circle
                      height={24}
                      width={24}
                      baseColor="#374151"
                      highlightColor="#4B5563"
                    />
                  </div>
                ))
              : recommendedTracks?.slice(0, 6).map((track: any) => (
                  <div
                    key={track._id}
                    className="group flex items-center space-x-3 rounded-lg p-2 transition-colors hover:bg-gray-200"
                  >
                    <div className="relative overflow-hidden rounded">
                      <ImageComponent
                        src={track.thumbnailUrl || '/assets/images/women.avif'}
                        alt={track.title}
                        className="h-full w-full object-cover"
                        figClassName="h-10 w-10"
                        fill
                      />
                      <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 opacity-0 transition-opacity group-hover:opacity-100">
                        <button
                          onClick={() =>
                            router.push(`/app/music/track/${track._id}`)
                          }
                          className="flex h-6 w-6 items-center justify-center rounded-full bg-white text-black"
                        >
                          <FaPlay className="ml-0.5 text-xs" />
                        </button>
                      </div>
                    </div>
                    <div className="min-w-0 flex-1">
                      <p className="truncate text-sm font-medium text-[#333333] xs:text-xs">
                        {track.title}
                      </p>
                      <p className="truncate text-xs text-[#666666]">
                        {track.artist || track.creator.displayName}
                      </p>
                    </div>
                    <div className="text-xs text-[#333333]">
                      {track.playCount || 0}
                    </div>
                  </div>
                ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TrackPage;
