'use client';
import { Ethereum } from '@/components/common/Icons';
import ImageComponent from '@/components/common/ImageComponent';
import Select from '@/components/ui/Select';
import TokenTable from '@/components/ui/TokenTable';
import React, { useState } from 'react';
import Profile from './Profile';
import TokenPerformanceChart from '@/components/ui/TokenPerformanceChart/TokenPerformanceChart';
import SocialLinks from './SocialLinks';
import Table from './Table';
import { useQuery } from '@tanstack/react-query';
import { tokenService } from '@/services/token.service';
import { PresaleTokenRes } from '@/types/token.interface';

const collectionOption = [
  { value: 'Last 1 Days', label: 'Last 1 Days' },
  { value: 'Last 24 Hours', label: 'Last 24 Hours' },
  { value: 'Last 7 Days', label: 'Last 7 Days' },
  { value: 'Last 1 Month', label: 'Last 1 Month' },
];
const collectionOptions = [
  { value: 'Last 24 Hours', label: 'Last 24 Hours' },
  { value: 'Trending - Select Time', label: 'Trending - Select Time' },
  { value: 'Last 7 Days', label: 'Last 7 Days' },
  { value: 'Last 1 Month', label: 'Last 1 Month' },
];
const collectionOptionss = [
  { value: 'Last 1 Days', label: 'Last 1 Days' },
  { value: 'Last 24 Hours', label: 'Last 24 Hours' },
  { value: 'Ranked By', label: 'Ranked By' },
  { value: 'Last 1 Month', label: 'Last 1 Month' },
];

// const columns = [
//   { key: 'tokens', label: 'Tokens' },
//   { key: 'price', label: 'Price' },
//   { key: 'age', label: 'Age' },
//   { key: 'txns', label: 'TXNS' },
//   { key: 'volume', label: 'Volume' },
//   { key: 'maker', label: 'Maker' },
//   { key: 'liquidity', label: 'Liquidity' },
//   { key: 'mcap', label: 'MCAP' },
// ];

const columns = [
  { key: 'tokens', label: 'Tokens' },
  { key: 'price', label: 'Price' },
  { key: 'market', label: 'Status' },
  { key: 'launch', label: 'Launch' },
];
// const Tabledata = [
//   {
//     tokens: (
//       <div className="flex items-center gap-1">
//         <Ethereum />
//         <ImageComponent
//           src="/assets/images/trading/fan1.svg"
//           height={30}
//           width={30}
//         />
//         <p className="text-base">Jon Doe Coin</p>
//       </div>
//     ),
//     price: '$0.023',
//     age: '7h',
//     txns: '127,664',
//     volume: '$21.4M',
//     maker: '16,394',
//     liquidity: '$198K',
//     mcap: '$1.4M',
//   },
//   {
//     tokens: (
//       <div className="flex items-center gap-1">
//         <Ethereum />
//         <ImageComponent
//           src="/assets/images/trading/fan1.svg"
//           height={30}
//           width={30}
//         />
//         <p className="text-base">Jon Doe Coin</p>
//       </div>
//     ),
//     price: '$0.023',
//     age: '7h',
//     txns: '127,664',
//     volume: '$21.4M',
//     maker: '16,394',
//     liquidity: '$198K',
//     mcap: '$1.4M',
//   },
//   {
//     tokens: (
//       <div className="flex items-center gap-1">
//         <Ethereum />
//         <ImageComponent
//           src="/assets/images/trading/fan1.svg"
//           height={30}
//           width={30}
//         />
//         <p className="text-base">Jon Doe Coin</p>
//       </div>
//     ),
//     price: '$0.023',
//     age: '7h',
//     txns: '127,664',
//     volume: '$21.4M',
//     maker: '16,394',
//     liquidity: '$198K',
//     mcap: '$1.4M',
//   },
//   {
//     tokens: (
//       <div className="flex items-center gap-1">
//         <Ethereum />
//         <ImageComponent
//           src="/assets/images/trading/fan1.svg"
//           height={30}
//           width={30}
//         />
//         <p className="text-base">Jon Doe Coin</p>
//       </div>
//     ),
//     price: '$0.023',
//     age: '7h',
//     txns: '127,664',
//     volume: '$21.4M',
//     maker: '16,394',
//     liquidity: '$198K',
//     mcap: '$1.4M',
//   },
//   {
//     tokens: (
//       <div className="flex items-center gap-1">
//         <Ethereum />
//         <ImageComponent
//           src="/assets/images/trading/fan1.svg"
//           height={30}
//           width={30}
//         />
//         <p className="text-base">Jon Doe Coin</p>
//       </div>
//     ),
//     price: '$0.023',
//     age: '7h',
//     txns: '127,664',
//     volume: '$21.4M',
//     maker: '16,394',
//     liquidity: '$198K',
//     mcap: '$1.4M',
//   },
//   {
//     tokens: (
//       <div className="flex items-center gap-1">
//         <Ethereum />
//         <ImageComponent
//           src="/assets/images/trading/fan1.svg"
//           height={30}
//           width={30}
//         />
//         <p className="text-base">Jon Doe Coin</p>
//       </div>
//     ),
//     price: '$0.023',
//     age: '7h',
//     txns: '127,664',
//     volume: '$21.4M',
//     maker: '16,394',
//     liquidity: '$198K',
//     mcap: '$1.4M',
//   },
//   {
//     tokens: (
//       <div className="flex items-center gap-1">
//         <Ethereum />
//         <ImageComponent
//           src="/assets/images/trading/fan1.svg"
//           height={30}
//           width={30}
//         />
//         <p className="text-base">Jon Doe Coin</p>
//       </div>
//     ),
//     price: '$0.023',
//     age: '7h',
//     txns: '127,664',
//     volume: '$21.4M',
//     maker: '16,394',
//     liquidity: '$198K',
//     mcap: '$1.4M',
//   },
//   {
//     tokens: (
//       <div className="flex items-center gap-1">
//         <Ethereum />
//         <ImageComponent
//           src="/assets/images/trading/fan1.svg"
//           height={30}
//           width={30}
//         />
//         <p className="text-base">Jon Doe Coin</p>
//       </div>
//     ),
//     price: '$0.023',
//     age: '7h',
//     txns: '127,664',
//     volume: '$21.4M',
//     maker: '16,394',
//     liquidity: '$198K',
//     mcap: '$1.4M',
//   },
//   {
//     tokens: (
//       <div className="flex items-center gap-1">
//         <Ethereum />
//         <ImageComponent
//           src="/assets/images/trading/fan1.svg"
//           height={30}
//           width={30}
//         />
//         <p className="text-base">Jon Doe Coin</p>
//       </div>
//     ),
//     price: '$0.023',
//     age: '7h',
//     txns: '127,664',
//     volume: '$21.4M',
//     maker: '16,394',
//     liquidity: '$198K',
//     mcap: '$1.4M',
//   },

//   {
//     tokens: (
//       <div className="flex items-center gap-1">
//         <Ethereum />
//         <ImageComponent
//           src="/assets/images/trading/fan1.svg"
//           height={30}
//           width={30}
//         />
//         <p className="text-base">Jon Doe Coin</p>
//       </div>
//     ),
//     price: '$0.023',
//     age: '7h',
//     txns: '127,664',
//     volume: '$21.4M',
//     maker: '16,394',
//     liquidity: '$198K',
//     mcap: '$1.4M',
//   },
//   {
//     tokens: (
//       <div className="flex items-center gap-1">
//         <Ethereum />
//         <ImageComponent
//           src="/assets/images/trading/fan1.svg"
//           height={30}
//           width={30}
//         />
//         <p className="text-base">Jon Doe Coin</p>
//       </div>
//     ),
//     price: '$0.023',
//     age: '7h',
//     txns: '127,664',
//     volume: '$21.4M',
//     maker: '16,394',
//     liquidity: '$198K',
//     mcap: '$1.4M',
//   },
//   {
//     tokens: (
//       <div className="flex items-center gap-1">
//         <Ethereum />
//         <ImageComponent
//           src="/assets/images/trading/fan1.svg"
//           height={30}
//           width={30}
//         />
//         <p className="text-base">Jon Doe Coin</p>
//       </div>
//     ),
//     price: '$0.023',
//     age: '7h',
//     txns: '127,664',
//     volume: '$21.4M',
//     maker: '16,394',
//     liquidity: '$198K',
//     mcap: '$1.4M',
//   },
//   {
//     tokens: (
//       <div className="flex items-center gap-1">
//         <Ethereum />
//         <ImageComponent
//           src="/assets/images/trading/fan1.svg"
//           height={30}
//           width={30}
//         />
//         <p className="text-base">Jon Doe Coin</p>
//       </div>
//     ),
//     price: '$0.023',
//     age: '7h',
//     txns: '127,664',
//     volume: '$21.4M',
//     maker: '16,394',
//     liquidity: '$198K',
//     mcap: '$1.4M',
//   },
//   {
//     tokens: (
//       <div className="flex items-center gap-1">
//         <Ethereum />
//         <ImageComponent
//           src="/assets/images/trading/fan1.svg"
//           height={30}
//           width={30}
//         />
//         <p className="text-base">Jon Doe Coin</p>
//       </div>
//     ),
//     price: '$0.023',
//     age: '7h',
//     txns: '127,664',
//     volume: '$21.4M',
//     maker: '16,394',
//     liquidity: '$198K',
//     mcap: '$1.4M',
//   },
// ];

const LiveToken = () => {
  const [selectedHour, setSelectedHour] = useState(collectionOption[0]);
  const [selectedTime, setSelectedTime] = useState(collectionOptions[1]);
  const [selectedRank, setSelectedRank] = useState(collectionOptionss[2]);
  const [showBackDiv, setShowBackDiv] = useState(false);
  const [page, setPage] = useState(1);
  const [limit] = useState(10);

  const queryFilters = {
    limit,
    page,
  };

  const { data: launchedToken, isLoading } = useQuery<PresaleTokenRes>({
    queryKey: ['presale-token', queryFilters],
    queryFn: () => tokenService.getLaunchedToken(queryFilters),
  });

  const launchedTokenData = launchedToken?.data?.data || [];

  const tableData = launchedTokenData.map((token) => ({
    tokens: (
      <div className="flex items-center gap-1">
        <Ethereum />
        <ImageComponent
          src={token.logoUrl || '/assets/images/admin/avatar.png'}
          fill
          figClassName="h-8 w-8"
          className="rounded-full object-cover"
        />
        <p className="text-base">
          {token.name} ({token.symbol})
        </p>
      </div>
    ),
    price: `${token.totalSupply} $`,
    market: token.status,
    launch: new Date(token.createdAt).toLocaleDateString(),
  }));

  const handlePageChange = (page: number) => {
    setPage(page);
  };
  const initialPage = launchedToken?.data?.page || 1;
  const totalPages = launchedToken?.data?.totalPages || 0;

  const handleTokenTableClick = () => {
    setShowBackDiv(true);
  };
  const visibleColumnKeys = ['txns', 'volume', 'maker', 'liquidity', 'mcap'];
  return (
    <>
      {!showBackDiv && (
        <div className="pb-5">
          <div className="mt-3.5 grid grid-cols-2 gap-5 sm:mt-12">
            <div className="flex justify-center rounded-[20px] bg-white py-3 shadow-[0_5px_10px_rgba(0,0,0,0.1)] sm:py-5">
              <p className="text-sm font-bold sm:text-base">
                24HVolume: <span className="text-primary">$10.31B</span>
              </p>
            </div>
            <div className="flex justify-center rounded-[20px] bg-white py-3 shadow-[0_5px_10px_rgba(0,0,0,0.1)] sm:py-5">
              <p className="text-sm font-bold sm:text-base">
                24HTxns: <span className="text-primary">31,744,234</span>
              </p>
            </div>
          </div>

          <div className="mt-5 flex flex-wrap gap-2.5 sm:mt-2.5">
            <Select
              options={collectionOption}
              selected={selectedHour}
              onSelect={setSelectedHour}
              placeholder=""
              className="w-full !rounded-[6px] !bg-white sm:max-w-[200px]"
            />

            <Select
              options={collectionOptions}
              selected={selectedTime}
              onSelect={setSelectedTime}
              placeholder=""
              className="!w-full !rounded-[6px] !bg-white sm:max-w-[200px]"
            />
            <Select
              options={collectionOptionss}
              selected={selectedRank}
              onSelect={setSelectedRank}
              placeholder=""
              className="!w-full !rounded-[6px] !bg-white sm:max-w-[200px]"
            />
          </div>

          <div
            className="mt-2.5 hidden cursor-pointer sm:block"
            onClick={handleTokenTableClick}
          >
            <TokenTable
              columns={columns}
              bg={true}
              data={tableData}
              presale={true}
              onPageChange={handlePageChange}
              initialPage={initialPage}
              totalPages={totalPages}
              loading={isLoading}
            />
          </div>

          <div
            className="mt-5 block cursor-pointer space-y-3 sm:hidden"
            onClick={handleTokenTableClick}
          >
            {tableData?.map((items: any, index) => (
              <div
                className="bg-white p-2.5 shadow-[0_5px_10px_rgba(0,0,0,0.1)]"
                key={index}
              >
                <div className="flex items-center justify-between font-semibold">
                  <span>{items.tokens}</span>
                  <span>{items.price}</span>
                </div>
                <div className="mt-2 flex items-center justify-between text-sm text-gray-700">
                  {columns
                    .filter((col) => visibleColumnKeys.includes(col.key))
                    .map((col, idx) => (
                      <div className="" key={idx}>
                        <div className="">{col.label}</div>
                        <div className="mt-1 text-primary">
                          {items[col.key] ?? '—'}
                        </div>
                      </div>
                    ))}
                </div>
              </div>
            ))}
          </div>
          <p className="mt-12 text-center text-sm text-[#666666]">
            © 2025 TEMOC • All Rights Reserved
          </p>
        </div>
      )}

      {showBackDiv && (
        <div className="back">
          <Profile setexploreDetail={setShowBackDiv} />
          <div className="mt-7 px-7">
            <TokenPerformanceChart />
          </div>
          <Table />
          <div className="pb-5 sm:px-7">
            <SocialLinks />
          </div>
        </div>
      )}
    </>
  );
};

export default LiveToken;
