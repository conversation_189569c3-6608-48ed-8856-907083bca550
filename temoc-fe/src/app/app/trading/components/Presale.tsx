import { Ethereum } from '@/components/common/Icons';
import ImageComponent from '@/components/common/ImageComponent';
import Select from '@/components/ui/Select';
import TokenTable from '@/components/ui/TokenTable';
import { tokenService } from '@/services/token.service';
import { PresaleTokenRes } from '@/types/token.interface';
import { useQuery } from '@tanstack/react-query';
import React, { useState } from 'react';

const collectionOption = [
  { value: 'Last 1 Days', label: 'Last 1 Days' },
  { value: 'Last 24 Hours', label: 'Last 24 Hours' },
  { value: 'Last 7 Days', label: 'Last 7 Days' },
  { value: 'Last 1 Month', label: 'Last 1 Month' },
];
const collectionOptions = [
  { value: 'Last 24 Hours', label: 'Last 24 Hours' },
  { value: 'Trending - Select Time', label: 'Trending - Select Time' },
  { value: 'Last 7 Days', label: 'Last 7 Days' },
  { value: 'Last 1 Month', label: 'Last 1 Month' },
];
const collectionOptionss = [
  { value: 'Last 1 Days', label: 'Last 1 Days' },
  { value: 'Last 24 Hours', label: 'Last 24 Hours' },
  { value: 'Ranked By', label: 'Ranked By' },
  { value: 'Last 1 Month', label: 'Last 1 Month' },
];

const columns = [
  { key: 'tokens', label: 'Tokens' },
  { key: 'price', label: 'Price' },
  { key: 'market', label: 'Status' },
  { key: 'launch', label: 'Launch' },
];
// const Tabledata = [
//   {
//     tokens: (
//       <div className="flex items-center gap-1">
//         <Ethereum />
//         <ImageComponent
//           src="/assets/images/trading/fan1.svg"
//           height={30}
//           width={30}
//         />
//         <p className="text-base">Jon Doe Coin</p>
//       </div>
//     ),
//     price: '$20B USDT',
//     market: 'Presale',
//     launch: 'In 1 month',
//     change: '$30B USDT',
//   },
//   {
//     tokens: (
//       <div className="flex items-center gap-1">
//         <Ethereum />
//         <ImageComponent
//           src="/assets/images/trading/fan1.svg"
//           height={30}
//           width={30}
//         />
//         <p className="text-base">Jon Doe Coin</p>
//       </div>
//     ),
//     price: '$20B USDT',
//     market: 'Presale',
//     launch: 'In 1 month',
//     change: '$30B USDT',
//   },
//   {
//     tokens: (
//       <div className="flex items-center gap-1">
//         <Ethereum />
//         <ImageComponent
//           src="/assets/images/trading/fan1.svg"
//           height={30}
//           width={30}
//         />
//         <p className="text-base">Jon Doe Coin</p>
//       </div>
//     ),
//     price: '$20B USDT',
//     market: 'Presale',
//     launch: 'In 1 month',
//     change: '$30B USDT',
//   },
//   {
//     tokens: (
//       <div className="flex items-center gap-1">
//         <Ethereum />
//         <ImageComponent
//           src="/assets/images/trading/fan1.svg"
//           height={30}
//           width={30}
//         />
//         <p className="text-base">Jon Doe Coin</p>
//       </div>
//     ),
//     price: '$20B USDT',
//     market: 'Presale',
//     launch: 'In 1 month',
//     change: '$30B USDT',
//   },
//   {
//     tokens: (
//       <div className="flex items-center gap-1">
//         <Ethereum />
//         <ImageComponent
//           src="/assets/images/trading/fan1.svg"
//           height={30}
//           width={30}
//         />
//         <p className="text-base">Jon Doe Coin</p>
//       </div>
//     ),
//     price: '$20B USDT',
//     market: 'Presale',
//     launch: 'In 1 month',
//     change: '$30B USDT',
//   },
//   {
//     tokens: (
//       <div className="flex items-center gap-1">
//         <Ethereum />
//         <ImageComponent
//           src="/assets/images/trading/fan1.svg"
//           height={30}
//           width={30}
//         />
//         <p className="text-base">Jon Doe Coin</p>
//       </div>
//     ),
//     price: '$20B USDT',
//     market: 'Presale',
//     launch: 'In 1 month',
//     change: '$30B USDT',
//   },
//   {
//     tokens: (
//       <div className="flex items-center gap-1">
//         <Ethereum />
//         <ImageComponent
//           src="/assets/images/trading/fan1.svg"
//           height={30}
//           width={30}
//         />
//         <p className="text-base">Jon Doe Coin</p>
//       </div>
//     ),
//     price: '$20B USDT',
//     market: 'Presale',
//     launch: 'In 1 month',
//     change: '$30B USDT',
//   },
//   {
//     tokens: (
//       <div className="flex items-center gap-1">
//         <Ethereum />
//         <ImageComponent
//           src="/assets/images/trading/fan1.svg"
//           height={30}
//           width={30}
//         />
//         <p className="text-base">Jon Doe Coin</p>
//       </div>
//     ),
//     price: '$20B USDT',
//     market: 'Presale',
//     launch: 'In 1 month',
//     change: '$30B USDT',
//   },
//   {
//     tokens: (
//       <div className="flex items-center gap-1">
//         <Ethereum />
//         <ImageComponent
//           src="/assets/images/trading/fan1.svg"
//           height={30}
//           width={30}
//         />
//         <p className="text-base">Jon Doe Coin</p>
//       </div>
//     ),
//     price: '$20B USDT',
//     market: 'Presale',
//     launch: 'In 1 month',
//     change: '$30B USDT',
//   },
//   {
//     tokens: (
//       <div className="flex items-center gap-1">
//         <Ethereum />
//         <ImageComponent
//           src="/assets/images/trading/fan1.svg"
//           height={30}
//           width={30}
//         />
//         <p className="text-base">Jon Doe Coin</p>
//       </div>
//     ),
//     price: '$20B USDT',
//     market: 'Presale',
//     launch: 'In 1 month',
//     change: '$30B USDT',
//   },
//   {
//     tokens: (
//       <div className="flex items-center gap-1">
//         <Ethereum />
//         <ImageComponent
//           src="/assets/images/trading/fan1.svg"
//           height={30}
//           width={30}
//         />
//         <p className="text-base">Jon Doe Coin</p>
//       </div>
//     ),
//     price: '$20B USDT',
//     market: 'Presale',
//     launch: 'In 1 month',
//     change: '$30B USDT',
//   },
//   {
//     tokens: (
//       <div className="flex items-center gap-1">
//         <Ethereum />
//         <ImageComponent
//           src="/assets/images/trading/fan1.svg"
//           height={30}
//           width={30}
//         />
//         <p className="text-base">Jon Doe Coin</p>
//       </div>
//     ),
//     price: '$20B USDT',
//     market: 'Presale',
//     launch: 'In 1 month',
//     change: '$30B USDT',
//   },
//   {
//     tokens: (
//       <div className="flex items-center gap-1">
//         <Ethereum />
//         <ImageComponent
//           src="/assets/images/trading/fan1.svg"
//           height={30}
//           width={30}
//         />
//         <p className="text-base">Jon Doe Coin</p>
//       </div>
//     ),
//     price: '$20B USDT',
//     market: 'Presale',
//     launch: 'In 1 month',
//     change: '$30B USDT',
//   },
//   {
//     tokens: (
//       <div className="flex items-center gap-1">
//         <Ethereum />
//         <ImageComponent
//           src="/assets/images/trading/fan1.svg"
//           height={30}
//           width={30}
//         />
//         <p className="text-base">Jon Doe Coin</p>
//       </div>
//     ),
//     price: '$20B USDT',
//     market: 'Presale',
//     launch: 'In 1 month',
//     change: '$30B USDT',
//   },
//   {
//     tokens: (
//       <div className="flex items-center gap-1">
//         <Ethereum />
//         <ImageComponent
//           src="/assets/images/trading/fan1.svg"
//           height={30}
//           width={30}
//         />
//         <p className="text-base">Jon Doe Coin</p>
//       </div>
//     ),
//     price: '$20B USDT',
//     market: 'Presale',
//     launch: 'In 1 month',
//     change: '$30B USDT',
//   },
// ];

const LiveToken = () => {
  const [selectedHour, setSelectedHour] = useState(collectionOption[0]);
  const [selectedTime, setSelectedTime] = useState(collectionOptions[1]);
  const [selectedRank, setSelectedRank] = useState(collectionOptionss[2]);
  const [page, setPage] = useState(1);
  const [limit] = useState(10);

  const queryFilters = {
    limit,
    page,
  };

  const visibleColumnKeys = ['market', 'launch', 'change'];

  const { data: presaleToken, isLoading } = useQuery<PresaleTokenRes>({
    queryKey: ['presale-token', queryFilters],
    queryFn: () => tokenService.getPresaleToken(queryFilters),
  });

  const presaleTokenData = presaleToken?.data?.data || [];

  const tableData = presaleTokenData.map((token) => ({
    tokens: (
      <div className="flex items-center gap-1">
        <Ethereum />
        <ImageComponent
          src={token.logoUrl || '/assets/images/admin/avatar.png'}
          fill
          figClassName="h-8 w-8"
          className="rounded-full object-cover"
        />
        <p className="text-base">
          {token.name} ({token.symbol})
        </p>
      </div>
    ),
    price: `${token.totalSupply} $`,
    market: token.status,
    launch: new Date(token.createdAt).toLocaleDateString(),
  }));

  const handlePageChange = (page: number) => {
    setPage(page);
  };
  const initialPage = presaleToken?.data?.page || 1;
  const totalPages = presaleToken?.data?.totalPages || 0;

  return (
    <>
      <div className="pb-5">
        <div className="mt-2.5 flex flex-wrap gap-2.5">
          <Select
            options={collectionOption}
            selected={selectedHour}
            onSelect={setSelectedHour}
            placeholder=""
            className="!rouned-[6px] w-full !bg-white sm:max-w-[200px]"
          />
          <Select
            options={collectionOptions}
            selected={selectedTime}
            onSelect={setSelectedTime}
            placeholder=""
            className="!rouned-[6px] w-full !bg-white sm:max-w-[200px]"
          />
          <Select
            options={collectionOptionss}
            selected={selectedRank}
            onSelect={setSelectedRank}
            placeholder=""
            className="!rouned-[6px] w-full !bg-white sm:max-w-[200px]"
          />
        </div>
        <div className="mt-2.5 hidden sm:block">
          <TokenTable
            columns={columns}
            bg={true}
            data={tableData}
            presale={true}
            onPageChange={handlePageChange}
            initialPage={initialPage}
            totalPages={totalPages}
            loading={isLoading}
          />
        </div>
        <div className="mt-5 block cursor-pointer space-y-3 sm:hidden">
          {tableData.map((items: any, index) => (
            <div
              className="bg-white p-2.5 shadow-[0_5px_10px_rgba(0,0,0,0.1)]"
              key={index}
            >
              <div className="flex items-center justify-between font-semibold">
                <span>{items.tokens}</span>
                <span>{items.price}</span>
              </div>
              <div className="mt-2 flex items-center justify-between text-sm text-gray-700">
                {columns
                  .filter((col) => visibleColumnKeys.includes(col.key))
                  .map((col, idx) => (
                    <div className="" key={idx}>
                      <div className="text-xs">{col.label}</div>
                      <div className="mt-1 text-primary">
                        {items[col.key] ?? '—'}
                      </div>
                    </div>
                  ))}
              </div>
            </div>
          ))}
        </div>
        <p className="mt-12 text-center text-sm text-[#666666]">
          © 2025 TEMOC • All Rights Reserved
        </p>
      </div>
    </>
  );
};

export default LiveToken;
