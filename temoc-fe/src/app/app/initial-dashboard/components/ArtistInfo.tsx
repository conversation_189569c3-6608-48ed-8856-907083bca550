'use client';
import React from 'react';
import 'react-toastify/dist/ReactToastify.css';
import { useQuery } from '@tanstack/react-query';
import { userService } from '@/services/user.service';
import { UserRes } from '@/types/user.interface';
import InfoSkeleton from '@/components/ui/Skelton/InfoSkelton';
import UpdateSocialMedia from '@/components/ui/UpdateSocialMedia';
const ArtistInfo = () => {
  const { data, isLoading } = useQuery<UserRes>({
    queryKey: ['profile-fan'],
    queryFn: () => userService?.getUsersProfile(),
  });

  const fanData = data?.data;

  const imagePreview =
    fanData?.role == 'artist'
      ? fanData?.artistProfile?.profilePic
      : fanData?.avatarUrl;
  const coverPreview =
    fanData?.role == 'artist'
      ? fanData?.artistProfile?.coverPhoto
      : fanData?.coverPicture;
  const fullName =
    fanData?.firstName && fanData?.lastName
      ? `${fanData.firstName} ${fanData.lastName}`
      : '<PERSON> Haskin';
  const hasCover = Boolean(coverPreview);

  return (
    <>
      {isLoading ? (
        <div className="h-[300px]">
          <InfoSkeleton />
        </div>
      ) : (
        <div
          className="relative h-[300px] bg-cover px-4 py-5 sm:px-8"
          style={{
            backgroundImage: coverPreview
              ? `url(${coverPreview})`
              : 'url(/assets/images/admin/info-upload.png)',
          }}
        >
          <div className="flex h-full w-full items-center justify-center gap-4 sm:items-end sm:justify-between">
            <div className="flex flex-col items-center gap-3 sm:flex-row">
              <div>
                <div className="relative w-[100px] sm:w-[160px]">
                  <div className="relative flex h-[100px] w-[100px] flex-col items-center justify-center rounded-full bg-white sm:h-[160px] sm:w-[160px]">
                    {imagePreview ? (
                      <>
                        <img
                          src={imagePreview}
                          alt="Profile Preview"
                          className="block h-full w-full rounded-full object-cover"
                        />
                      </>
                    ) : (
                      <>
                        <img
                          src="/assets/images/avatar.png"
                          alt="Profile Preview"
                          className="block h-full w-full rounded-full object-cover"
                        />
                      </>
                    )}
                  </div>
                </div>
                <UpdateSocialMedia
                  ArtistData={fanData?.artistProfile}
                  mode={true}
                />
              </div>
              <div>
                <h3
                  className={`w-full truncate text-center text-xl font-semibold capitalize sm:max-w-[400px] sm:text-start sm:text-[30px] ${
                    hasCover ? 'text-white' : 'text-[#333333]'
                  }`}
                >
                  {fullName}
                </h3>
                <div className="mt-2">
                  <p
                    className={`line-clamp-3 w-full text-center text-xs font-medium sm:max-w-[256px] sm:text-start ${
                      hasCover ? 'text-white' : 'text-[#333333]'
                    }`}
                  >
                    {fanData?.role == 'fan'
                      ? fanData?.description || ''
                      : fanData?.artistProfile.bio || ''}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default ArtistInfo;
