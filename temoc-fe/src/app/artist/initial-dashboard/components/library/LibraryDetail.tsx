'use client';
import React, { useEffect, useRef, useState } from 'react';
import { Button } from '@/components/common';
import ImageComponent from '@/components/common/ImageComponent';
import { Heart, MessageCircle, Share2 } from 'lucide-react';
import TokenTable from '@/components/ui/TokenTable';
import { FaPlay } from 'react-icons/fa6';
import { IoPause } from 'react-icons/io5';
import MobileLibraryItem from '@/components/ui/MobileLibrary';
import { useQuery } from '@tanstack/react-query';
import { libraryService } from '@/services/library.service';
import { Index } from 'viem';
import { GiPreviousButton } from 'react-icons/gi';
import { GiNextButton } from 'react-icons/gi';
import { RxLoop } from 'react-icons/rx';
// import { IoShuffleOutline } from 'react-icons/io5';

interface Props {
  onAddAlbum: () => void;
  onAddSongs: () => void; // ← New prop
  card?: boolean;
  library: any;
  album?: any;
}

const columns = [
  { key: 'Songs', label: 'Songs' },
  { key: 'plays', label: 'Plays' },
  { key: 'album', label: 'Album' },
  { key: 'duration', label: 'Duration' },
  { key: 'share', label: 'Share' },
];

const LibraryDetail = ({ onAddSongs, album, library }: Props) => {
  const audioRef = useRef<HTMLAudioElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [showControls, setShowControls] = useState(false);
  const [currentTrackUrl, setCurrentTrackUrl] = useState<string | null>(null);
  const [progress, setProgress] = useState(0); // current time
  const [duration, setDuration] = useState(0); // total duration
  const [isLooping, setIsLooping] = useState(false);
  // const [randomPlayed, setRandomPlayed] = useState(false);

  console.log(library, 'library');

  const { data: tracksData } = useQuery({
    queryFn: () => libraryService.getTracks(album?._id),
    queryKey: ['tracks', album?._id],
    enabled: !!album?._id,
  });

  const tracks = tracksData?.data?.tracks || [];
  console.log(tracks, 'tracks');

  const togglePlay = (url: string) => {
    const audioEl = audioRef.current;
    if (!audioEl) return;

    if (currentTrackUrl !== url) {
      audioEl.src = url;
      setCurrentTrackUrl(url);
      audioEl.play();
      setIsPlaying(true);
      setShowControls(true);
    } else {
      if (isPlaying) {
        audioEl.pause();
        setIsPlaying(false);
      } else {
        audioEl.play();
        setIsPlaying(true);
      }
    }
  };

  const handleEnded = () => {
    setIsPlaying(false);
    setShowControls(false);
  };

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const updateProgress = () => {
      setProgress(audio.currentTime);
      setDuration(audio.duration);
    };

    audio.addEventListener('timeupdate', updateProgress);
    audio.addEventListener('loadedmetadata', updateProgress);

    return () => {
      audio.removeEventListener('timeupdate', updateProgress);
      audio.removeEventListener('loadedmetadata', updateProgress);
    };
  }, [currentTrackUrl]);

  const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
    const audio = audioRef.current;
    if (!audio) return;

    const newTime = parseFloat(e.target.value);
    audio.currentTime = newTime;
    setProgress(newTime);
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds < 10 ? '0' + seconds : seconds}`;
  };

  const handleNext = () => {
    const currentIndex = tracks.findIndex(
      (t: any) => t.fileUrl === currentTrackUrl,
    );
    const nextIndex = (currentIndex + 1) % tracks.length;
    const nextTrack = tracks[nextIndex];
    if (nextTrack) {
      togglePlay(nextTrack.fileUrl);
    }
  };

  const handlePrev = () => {
    const currentIndex = tracks.findIndex(
      (t: any) => t.fileUrl === currentTrackUrl,
    );
    const prevIndex = (currentIndex - 1 + tracks.length) % tracks.length;
    const prevTrack = tracks[prevIndex];
    if (prevTrack) {
      togglePlay(prevTrack.fileUrl);
    }
  };

  const toggleLoop = () => {
    setIsLooping(!isLooping);
  };

  // const playRandomSong = () => {
  //   if (!tracks || tracks.length === 0) return;

  //   const randomIndex = Math.floor(Math.random() * tracks.length);
  //   const randomTrack = tracks[randomIndex];

  //   if (randomTrack?.fileUrl) {
  //     togglePlay(randomTrack?.fileUrl);
  //     setRandomPlayed(true); // Set green state after success
  //   }
  // };

  const Tabledata = tracks.map((track: any, i: Index) => ({
    Songs: (
      <div
        className={`flex flex-col items-center gap-2 sm:flex-row`}
        onClick={() => {
          togglePlay(track?.fileUrl);
          setShowControls(true);
        }}
      >
        {i + 1}
        <div
          className="group relative w-fit cursor-pointer"
          // onClick={() => togglePlay(track?.fileUrl)}
        >
          <ImageComponent
            src={track?.thumbnailUrl || '/assets/images/trading/tableimg.svg'}
            figClassName="h-10 w-10 flex-shrink-0 "
            fill
            className="object-cover"
          />
          <div className="absolute left-1/2 top-1/2 hidden h-5 w-5 -translate-x-1/2 -translate-y-1/2 items-center justify-center rounded-full bg-primary group-hover:flex">
            {isPlaying && currentTrackUrl === track?.fileUrl ? (
              <IoPause className="text-[12px] text-white" />
            ) : (
              <FaPlay className="text-[10px] text-white" />
            )}
          </div>
        </div>
        <p
          className="inline-block max-w-[150px] cursor-pointer truncate align-bottom text-sm"
          title={track?.title}
        >
          {track?.title}
        </p>
      </div>
    ),
    plays: `${track?.playCount || 0}`,
    album: `${album?.title}`,
    duration: `${track?.duration.toFixed(1) || 0}`,
    share: (
      <div className="flex items-center gap-2">
        <div className="flex flex-col items-center">
          <Heart className="h-4 w-4 text-red-500" />
          <span className="text-[10px] text-[#666666]">25K</span>
        </div>
        <div className="flex flex-col items-center">
          <Share2 className="h-4 w-4" />
          <span className="text-[10px] text-[#666666]">125K</span>
        </div>
        <div className="flex flex-col items-center">
          <MessageCircle className="h-4 w-4" />
          <span className="text-[10px] text-[#666666]">100</span>
        </div>
      </div>
    ),
    trackUrl: track?.fileUrl,
  }));
  // const [cardClicked, setCardClicked] = useState(false);

  // const handleCardClick = () => {
  //   setCardClicked(true);
  // };

  return (
    <div>
      <div className="mt-5 flex flex-col justify-between gap-3 md:flex-row md:gap-5">
        <div className="flex gap-2">
          <ImageComponent
            src={album?.thumbnailUrl || '/assets/images/admin/avatar.png'}
            figClassName="h-10 w-10 flex-shrink-0 rounded-lg"
            fill
            alt=""
            priority
            className="rounded-full object-cover"
          />
          <div className="">
            <p
              className="inline-block max-w-[150px] truncate align-bottom text-[22px] font-medium text-[#333333]"
              title={library?.title}
            >
              {library?.title}
            </p>
            <p className="mt-1 text-sm text-[#666666] lg:max-w-[500px]">
              {library?.description ||
                'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.'}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-5 md:mt-2 md:gap-10 xs1:gap-2.5">
          <div>
            <p className="text-xs text-[#666666]"> Type:</p>
            <p className="text-sm text-primary">{library?.type}</p>
          </div>
          <div>
            <p className="text-xs text-[#666666]"> Genres:</p>
            <p className="text-sm text-primary">{library?.genre}</p>
          </div>
          <div>
            <p className="text-xs text-[#666666]">Songs:</p>
            <p className="text-sm text-primary"> {tracks?.length}</p>
          </div>

          <Button
            className="!h-10 !text-xs xs:ml-auto"
            arrow
            onClick={() => {
              onAddSongs();
            }}
          >
            ADD SONGS
          </Button>
        </div>
      </div>
      <div>
        {showControls && (
          <div className="fixed bottom-0 left-0 right-0 z-50 flex items-center justify-between bg-black px-8 py-2.5">
            <div className="flex items-center gap-2 text-white">
              <ImageComponent
                src={
                  tracks.find((t: any) => t?.fileUrl === currentTrackUrl)
                    ?.thumbnailUrl || '/assets/images/placeholder.avif'
                }
                figClassName="h-10 w-10 flex-shrink-0 "
                fill
                className="rounded-md object-cover"
                alt="Track Thumbnail"
              />
              <p className="inline-block max-w-[80px] truncate text-sm">
                {tracks.find((t: any) => t?.fileUrl === currentTrackUrl)
                  ?.title || 'Playing...'}
              </p>
            </div>
            <div className="flex w-full max-w-[560px] flex-col items-center gap-2">
              <div className="flex items-center gap-3">
                {/* <button
                    className={`text-base ${
                      randomPlayed ? 'text-green-600' : 'text-[#B2B2B2]'
                    }`}
                    onClick={playRandomSong}
                  >
                    <IoShuffleOutline className="text-base" />
                  </button> */}
                <button
                  onClick={handlePrev}
                  className="text-white hover:scale-110"
                >
                  <GiPreviousButton className="text-base text-[#B2B2B2]" />
                </button>
                <div
                  onClick={() => {
                    if (isPlaying) {
                      audioRef.current?.pause();
                      setIsPlaying(false);
                    } else {
                      audioRef.current?.play();
                      setIsPlaying(true);
                    }
                  }}
                  className={`flex h-8 w-8 items-center justify-center rounded-full ${isPlaying ? 'bg-primary' : 'bg-white'}`}
                >
                  {isPlaying ? (
                    <IoPause className="text-sm text-white" />
                  ) : (
                    <FaPlay className="text-sm" />
                  )}
                </div>
                <button
                  onClick={handleNext}
                  className="text-white hover:scale-110"
                >
                  <GiNextButton className="text-base text-[#B2B2B2]" />
                </button>

                {/* Loop Toggle */}
                <button
                  onClick={toggleLoop}
                  className={`text-xs text-[#B2B2B2] ${isLooping ? 'font-bold text-green-400' : ''}`}
                  title="Toggle Loop"
                >
                  <RxLoop className="text-base" />
                </button>
              </div>

              <div className="flex w-full items-center gap-2">
                <span className="text-xs text-[#B2B2B2]">
                  {formatTime(progress)}
                </span>
                <input
                  type="range"
                  min="0"
                  max={duration || 0}
                  value={progress}
                  onChange={handleSeek}
                  className="w-full bg-[#777777] accent-[#FF6E00]"
                />
                <span className="text-xs text-[#B2B2B2]">
                  {formatTime(duration)}
                </span>
                {/* Next */}
              </div>
            </div>
            <div></div>
          </div>
        )}
        <div className="mt-6 hidden w-full sm:block">
          <TokenTable
            columns={columns}
            data={Tabledata}
            isPlaying={isPlaying}
            currentTrackUrl={currentTrackUrl}
          />

          <audio ref={audioRef} onEnded={handleEnded} loop={isLooping} />
        </div>
        <div className="mt-3 block sm:hidden">
          {tracks.map((song: any, idx: Index) => (
            <MobileLibraryItem
              key={idx}
              index={idx} // use the map index here
              track={song}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default LibraryDetail;
