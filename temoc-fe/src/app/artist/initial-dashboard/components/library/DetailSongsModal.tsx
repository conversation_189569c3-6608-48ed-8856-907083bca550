'use client';
import React, { useEffect, useRef, useState } from 'react';
import { Input } from '@/components/common/Forms/Input';
import SelectComponent from '@/components/common/Forms/Select';
import { Textarea } from '@/components/common/Forms/TextArea';
import Upload from '@/components/common/Icons/Upload';
import { Button } from '@/components/common';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { libraryService } from '@/services/library.service';
import { toast } from 'react-toastify';
import { Controller, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { TrackSchema } from '@/utils/schema';
import InputError from '@/components/common/Forms/InputError';
import TagInput from './TagInput';

interface Iprops {
  setOpenModal: React.Dispatch<React.SetStateAction<boolean>>;
  selectedTrack?: any;
  albumId?: string;
  album?: any;
  onBack?: () => void;
  handleFileRemove?: any;
  updateSelectedTrack?: any;
  setUpdatedRecords?: React.Dispatch<React.SetStateAction<any[]>>;
  setRecordFiles?: React.Dispatch<React.SetStateAction<any>>;
}
const DetailSongsModal = ({
  setOpenModal,
  handleFileRemove,
  albumId,
  album,
  onBack,
  setRecordFiles,
  selectedTrack,
  setUpdatedRecords,
  updateSelectedTrack,
}: Iprops) => {
  const [fileName, setFileName] = useState<string | null>(null);
  const inputRef = useRef<HTMLInputElement | null>(null);
  const [file, setFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const queryClient = useQueryClient();
  const [tags, setTags] = useState<string[]>([]);

  const genreOptions = [
    { value: 'Blue', label: 'Blue' },
    { value: 'Pop', label: 'Pop' },
    { value: 'Rock', label: 'Rock' },
  ];

  const {
    register,
    control,
    handleSubmit,
    reset,
    setValue,
    // watch,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(TrackSchema),
    defaultValues: {
      title: '',
      artist: '',
      description: '',
      genre: genreOptions[0],
      privacy: 'public',
      isFree: false,
    },
  });

  useEffect(() => {
    if (selectedTrack) {
      setValue(
        'title',
        selectedTrack?.file?.name ||
          selectedTrack?.name ||
          selectedTrack?.title,
      );
    }
  }, [selectedTrack, reset]);

  useEffect(() => {
    if (updateSelectedTrack) {
      setValue('artist', updateSelectedTrack?.artist || '');
      setValue('description', updateSelectedTrack?.description || '');
      setValue('privacy', updateSelectedTrack?.privacy || 'Public');
      setValue('isFree', updateSelectedTrack?.isFree || false);
      setValue(
        'genre',
        updateSelectedTrack?.genre
          ? {
              value: updateSelectedTrack.genre,
              label:
                updateSelectedTrack.genre.charAt(0).toUpperCase() +
                updateSelectedTrack.genre.slice(1),
            }
          : null,
      );
      setFileName(updateSelectedTrack?.file?.name || updateSelectedTrack?.name);
      setPreviewUrl(updateSelectedTrack?.thumbnailUrl);
    }
  }, [updateSelectedTrack]);

  const createTrackMutation = useMutation({
    mutationFn: (data: any) => libraryService.createTracks(data),
    onSuccess: (response) => {
      toast.success('Track created successfully');
      if (setUpdatedRecords) {
        setUpdatedRecords((prev: any) => [
          ...prev,
          {
            id: selectedTrack?.id,
            name: selectedTrack?.file?.name || selectedTrack?.name,
            track: response?.data?.track, // <-- use API response here
          },
        ]);
      }

      if (album?.length === 1 && onBack) {
        // onBack();

        if (selectedTrack?.id) {
          if (handleFileRemove) {
            // handleFileRemove(selectedTrack.id);
          }
        } else {
          if (setRecordFiles) {
            // setRecordFiles([]);
          }
        }
      }

      queryClient.refetchQueries({ queryKey: ['tracks'] });
      setOpenModal(false);
    },
    onError: (error: any) => {
      console.log('Error:', error);
      toast.error(error?.response?.data?.message);
    },
  });

  const UpdateTrackMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) =>
      libraryService.updateTrack(id, data),

    onSuccess: (response) => {
      toast.success('Track updated successfully');
      console.log('API Response:', response);

      if (setUpdatedRecords) {
        setUpdatedRecords((prev: any) => {
          const isExisting = prev.some(
            (r: any) =>
              (r.id && r.id === selectedTrack?.id) ||
              (!selectedTrack?.id &&
                r.name === (selectedTrack?.file?.name || selectedTrack?.name)),
          );

          if (isExisting) {
            // Update existing record
            return prev.map((r: any) => {
              const isMatch =
                (r.id && r.id === selectedTrack?.id) ||
                (!selectedTrack?.id &&
                  r.name ===
                    (selectedTrack?.file?.name || selectedTrack?.name));

              return isMatch
                ? {
                    ...r,
                    track: response?.data?.track,
                  }
                : r;
            });
          } else {
            // Add new record
            return [
              ...prev,
              {
                id: selectedTrack?.id,
                name: selectedTrack?.file?.name || selectedTrack?.name,
                track: response?.data?.track,
              },
            ];
          }
        });
      }

      if (album?.length === 1 && onBack) {
        // onBack();

        if (selectedTrack?.id) {
          if (handleFileRemove) {
            // handleFileRemove(selectedTrack.id);
          }
        } else {
          if (setRecordFiles) {
            // setRecordFiles([]);
          }
        }
      }

      queryClient.refetchQueries({ queryKey: ['tracks'] });
      setOpenModal(false);
    },

    onError: (error: any) => {
      console.log('Error:', error);
      toast.error(error?.response?.data?.message);
    },
  });

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selected = e.target.files?.[0];
    if (selected) {
      setFileName(selected.name);
      setFile(selected);
      const preview = URL.createObjectURL(selected);
      setPreviewUrl(preview);
    }
  };
  const onSubmit = (data: any) => {
    const formData = new FormData();
    formData.append('albumId', albumId ?? '');
    formData.append('title', data.title);
    formData.append('artist', data.artist);
    formData.append('tags', JSON.stringify(tags));
    formData.append('description', data.description);
    formData.append('privacy', data.privacy);
    formData.append('audioFile', selectedTrack?.file || selectedTrack);

    if (data.genre?.value) {
      formData.append('genre', data.genre.value);
    }
    if (file) {
      formData.append('thumbnail', file);
    }
    if (updateSelectedTrack) {
      UpdateTrackMutation.mutate({
        id: updateSelectedTrack?._id,
        data: formData,
      });
    } else {
      createTrackMutation.mutate(formData);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <div className="m-auto max-w-[690px]">
        <h3 className="text-center text-2xl font-semibold text-[#333333] sm:text-[30px]">
          Add Track
        </h3>
        <div className="mt-5 space-y-4 bg-white sm:p-5">
          <label className="flex h-[220px] w-[220px] cursor-pointer flex-col items-center justify-center gap-2 rounded-[14px] border border-[#CECECE] p-3 transition hover:border-primary">
            {previewUrl ? (
              <img
                src={previewUrl}
                alt="Thumbnail Preview"
                className="h-full w-full rounded-[14px] object-cover"
              />
            ) : (
              <>
                <h3 className="max-w-[170px] truncate text-center text-xs text-[#777777]">
                  Upload Thumbnail
                </h3>
                <Upload />
                <div>
                  <p className="text-center text-xs text-[#666666]">
                    {fileName || 'Upload Thumbnail Image'}
                  </p>
                  <p className="-mt-1 text-center text-[8px] text-[#666666]">
                    900px by 900px Recommended
                  </p>
                </div>
              </>
            )}
            <input
              type="file"
              accept="image/*"
              ref={inputRef}
              onChange={handleFileChange}
              className="hidden"
            />
          </label>
          <div>
            <div>
              <p className="mb-1">Track Title</p>
              <Input
                placeholder="Track Title"
                name="title"
                register={register}
              />
              <InputError error={errors.title?.message} />
            </div>
          </div>
          <div>
            <p className="mb-1">Artist/Band</p>
            <Input
              placeholder="Artist/Band"
              name="artist"
              register={register}
            />
            <InputError error={errors.artist?.message} />
          </div>
          <div>
            <p className="mb-1">Type of Genres</p>
            <Controller
              control={control}
              name="genre"
              render={({ field }) => (
                <SelectComponent
                  options={genreOptions}
                  selected={field.value}
                  onSelect={field.onChange}
                  placeholder="Type of Genres"
                  className="w-full"
                />
              )}
            />
          </div>
          <TagInput tags={tags} setTags={setTags} />
          <div>
            <p className="mb-1">Description (Optional)</p>
            <Textarea
              name={'description'}
              placeholder="Description (Optional)"
              className="h-[150px]"
              register={register}
            />
          </div>
        </div>
        <div className="mt-5 flex w-full flex-wrap justify-center gap-2">
          <Button
            type="submit"
            className="!h-[62px] !w-[315px] !text-base !font-bold xs:!w-full"
            arrow
            disabled={
              UpdateTrackMutation?.isPending || createTrackMutation?.isPending
            }
            isLoading={
              UpdateTrackMutation?.isPending || createTrackMutation?.isPending
            }
          >
            {updateSelectedTrack ? 'Update' : 'Save'}
          </Button>
        </div>
      </div>
    </form>
  );
};

export default DetailSongsModal;
