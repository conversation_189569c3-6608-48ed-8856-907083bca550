import React, { useState, useRef, useEffect } from 'react';
import { X, Copy } from 'lucide-react';
import { Input } from '@/components/common/Forms/Input';

// Define the props type
interface TagInputProps {
  tags: string[];
  setTags: React.Dispatch<React.SetStateAction<string[]>>;
}

const TagInput: React.FC<TagInputProps> = ({ tags, setTags }) => {
  const [inputValue, setInputValue] = useState<string>('');
  const [showCopySuccess, setShowCopySuccess] = useState<boolean>(false);
  const inputRef = useRef<HTMLInputElement | null>(null);
  const maxTags = 500;
  const currentLength = tags.join(', ').length;

  const removeTag = (indexToRemove: number) => {
    setTags(tags.filter((_, index) => index !== indexToRemove));
  };

  const addTag = (tagText: string) => {
    const newTag = tagText.trim();
    if (newTag && !tags.includes(newTag)) {
      setTags([...tags, newTag]);
    }
  };

  const handleInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' || e.key === ',') {
      e.preventDefault();
      if (inputValue.trim()) {
        addTag(inputValue);
        setInputValue('');
      }
    } else if (e.key === 'Backspace' && inputValue === '' && tags.length > 0) {
      removeTag(tags.length - 1);
    }
  };

  const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault();
    const pastedText = e.clipboardData.getData('text');
    const newTags = pastedText
      .split(/[,\n\r\t]+/)
      .map((tag) => tag.trim())
      .filter((tag) => tag && !tags.includes(tag));

    setTags([...tags, ...newTags]);
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(tags.join(', '));
      setShowCopySuccess(true);
      setTimeout(() => setShowCopySuccess(false), 2000);
    } catch (err) {
      console.error('Failed to copy tags:', err);
    }
  };

  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, []);

  return (
    <div className="mx-auto max-w-2xl text-black">
      <div className="mb-4">
        <p className="mb-1">Tags</p>
        <p className="mb-4 text-sm text-gray-400">
          Tags can be useful if content in your video is commonly misspelt.
          Otherwise, tags play a minimal role in helping viewers to find your
          video.
        </p>
      </div>

      <div className="relative rounded-md border border-[#CECECE]">
        {/* Copy buttons */}
        {tags.length > 0 && (
          <div className="absolute right-2 top-2 flex gap-2">
            <button
              type="button"
              onClick={copyToClipboard}
              className="rounded p-1.5 text-gray-400 transition-colors hover:bg-gray-600 hover:text-white"
              title="Copy tags"
            >
              <Copy size={16} />
            </button>
            <button
              onClick={() => setTags([])}
              className="rounded p-1.5 text-gray-400 transition-colors hover:bg-gray-600 hover:text-white"
              title="Clear all tags"
            >
              <X size={16} />
            </button>
          </div>
        )}

        {/* Tags display */}
        <div
          className={`${tags.length > 0 ? 'p-4 !pb-0 pr-20' : ''} flex flex-wrap gap-2`}
        >
          {tags.map((tag, index) => (
            <span
              key={index}
              className="inline-flex items-center gap-1 rounded-full bg-gray-600 px-3 py-1 text-sm text-white transition-colors hover:bg-gray-500"
            >
              {tag}
              <button
                onClick={() => removeTag(index)}
                className="ml-1 text-gray-300 hover:text-white"
              >
                <X size={14} />
              </button>
            </span>
          ))}
        </div>

        {/* Input field */}
        <Input
          ref={inputRef}
          name=""
          type="text"
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyDown={handleInputKeyDown}
          onPaste={handlePaste}
          placeholder="Enter a comma after each tag"
          className="!border-none"
        />
      </div>

      {/* Character counter */}
      <div className="mt-2 flex justify-end">
        <span
          className={`text-sm ${currentLength > maxTags ? 'text-red-400' : 'text-gray-400'}`}
        >
          {currentLength}/{maxTags}
        </span>
      </div>

      {/* Copy success message */}
      {showCopySuccess && (
        <div className="mt-2 rounded bg-green-600 p-2 text-sm text-white">
          Tags copied to clipboard!
        </div>
      )}

      {/* Instructions */}
      <div className="text-gray-400">
        <p className="text-xs">• Press Enter or comma to add a tag</p>
        <p className="text-xs">• Click X to remove individual tags</p>
        <p className="text-xs">
          • Paste multiple tags separated by commas, newlines, or tabs
        </p>
        <p className="text-xs">
          • Use copy buttons to copy current tags or duplicate them
        </p>
      </div>
    </div>
  );
};

export default TagInput;
