import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { tokenService } from '@/services/token.service';
import { copyToClipboard, truncateAddress } from '@/lib/functions';
import { Button } from '@/components/common';
import { MdArrowOutward } from 'react-icons/md';
import NoData from '@/components/ui/NoData';
import { Copy, TokenCreation } from '@/components/common/Icons';
import Loader from '@/components/common/Loader';
import { useAuth } from '@/hooks/useAuth';
import { presaleService } from '@/services/presale.service';
import { isBefore } from 'date-fns';

interface Iprops {
  setCreating: React.Dispatch<React.SetStateAction<boolean>>;
  setCurrentStep: React.Dispatch<React.SetStateAction<any>>;
  setTAddress: React.Dispatch<React.SetStateAction<string>>;
  setAfterCreateToken: (value: true | false) => void;
  setData: any;
}

const TokenPreview = ({
  setCreating,
  setCurrentStep,
  setTAddress,
  setAfterCreateToken,
  setData,
}: Iprops) => {
  const { user } = useAuth();

  const { data, isLoading, isError } = useQuery({
    queryKey: ['token'],
    queryFn: () => tokenService.getToken(),
  });

  const tokenData = data?.data;

  const { data: presaleD } = useQuery({
    queryKey: ['presale', user?._id],
    queryFn: () => presaleService.getPresale(user?._id as string),
    enabled: !!user?._id,
  });

  const presaleData = presaleD?.data || {};

  const formatDecimals = (decimals: number) => {
    if (decimals > 1e9) return decimals.toExponential(2);
    return decimals.toString();
  };

  if (isLoading) {
    return (
      <div className="px-6 py-10 text-center">
        <Loader />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="p-6 text-center text-red-500">
        Error fetching tokens. Please try again later.
      </div>
    );
  }

  if (!tokenData || tokenData.length === 0) {
    return (
      <NoData
        icon={<TokenCreation className="h-10 w-10" color="#333333" />}
        heading="No Token Yet. Create Your Token"
        description="Get Started by Creating Your Unique Token and Empower Your Fanbase."
      />
    );
  }

  const now = new Date();
  const presaleEnd = new Date(presaleData?.presaleEnd);
  const showLaunchButton = isBefore(now, presaleEnd);

  console.log(tokenData, 'tokenstatus');

  return (
    <div className="space-y-4 py-6 sm:p-6">
      {tokenData
        .slice() // Creates a shallow copy of the array
        .reverse() // Reverses the array to show the latest token at the top
        .map((token: any) => (
          <div
            key={token._id}
            className="bg-card text-card-foreground rounded-lg border border-l-4 border-l-blue-500 shadow-sm"
          >
            <div className="p-4">
              <div className="grid grid-cols-2 items-center gap-4 md:grid-cols-3 xl:grid-cols-6">
                <div className="flex flex-col items-start gap-2 space-y-1 sm:flex-row sm:items-center">
                  <img
                    src={token.logoUrl || '/assets/images/placeholder.avif'}
                    alt={token.name}
                    className="h-8 w-8 flex-shrink-0 rounded-full"
                  />
                  <div className="space-y-1 truncate">
                    <h4 className="truncate text-lg font-semibold capitalize">
                      {token.name}
                    </h4>
                    <p className="text-muted-foreground text-xs">
                      <strong>Symbol:</strong> {token.symbol.toUpperCase()}
                    </p>
                    <div className="flex items-center gap-2">
                      <p className="text-muted-foreground text-xs">
                        <strong>Token Address:</strong>{' '}
                        {truncateAddress(token.address)}
                      </p>
                      <div
                        className="cursor-pointer hover:text-primary"
                        onClick={() => copyToClipboard(token.address)}
                      >
                        <Copy />
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <p className="text-muted-foreground text-xs">
                        <strong>Wallet:</strong>{' '}
                        {truncateAddress(token.walletAddress)}
                      </p>
                      <div
                        className="cursor-pointer hover:text-primary"
                        onClick={() => copyToClipboard(token.walletAddress)}
                      >
                        <Copy />
                      </div>
                    </div>
                  </div>
                </div>

                <div className="text-end md:text-center">
                  <p className="text-muted-foreground text-sm">Decimals</p>
                  <p className="font-semibold">
                    {formatDecimals(token.decimals)}
                  </p>
                </div>

                <div className="md:text-center">
                  <p className="text-muted-foreground text-sm">Total Supply</p>
                  <p className="font-semibold">{token.totalSupply}</p>
                </div>

                <div className="text-end md:text-left xl:text-center">
                  <p className="text-muted-foreground text-sm">Network</p>
                  <p className="font-semibold">{token.network}</p>
                </div>

                <div className="md:text-center">
                  <p className="text-muted-foreground text-sm">Created At</p>
                  <p className="font-semibold">
                    {new Date(token.createdAt).toLocaleDateString()}
                  </p>
                </div>
                <div className="md:text-center">
                  <p className="text-muted-foreground text-sm">Status</p>
                  <p className="font-semibold capitalize">{token?.status}</p>
                </div>
              </div>
              <div className="mt-4 flex justify-end gap-2 xs:flex-col">
                <Button
                  className={`${(showLaunchButton || token?.status == 'launched') && '!hidden'} group !text-xs font-semibold uppercase`}
                  onClick={() => {
                    setCreating(true);
                    setCurrentStep('liquidity');
                  }}
                >
                  Launch Token
                  <MdArrowOutward className="text-sm text-white" />
                </Button>
                <Button
                  className={`${(token?.status == 'launched' || token?.status == 'presale') && '!hidden'} group !text-xs font-semibold uppercase`}
                  onClick={() => {
                    setCreating(true);
                    setCurrentStep('fair-launch');
                    setTAddress(token.address);
                  }}
                >
                  Launch Presale
                  <MdArrowOutward className="text-sm text-white" />
                </Button>

                {token?.status == 'launched' && (
                  <Button
                    className="group !text-xs font-semibold uppercase"
                    onClick={() => {
                      setCreating(true);
                      setCurrentStep('liquidity');
                      setData('wallet-connect');
                    }}
                  >
                    View Details
                    <MdArrowOutward className="text-sm text-white" />
                  </Button>
                )}
                {token?.status == 'presale' && (
                  <Button
                    className="group !text-xs font-semibold uppercase"
                    onClick={() => {
                      setAfterCreateToken(true);
                    }}
                  >
                    View Details
                    <MdArrowOutward className="text-sm text-white" />
                  </Button>
                )}
              </div>
            </div>
          </div>
        ))}
    </div>
  );
};

export default TokenPreview;
