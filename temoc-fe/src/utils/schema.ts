import * as yup from 'yup';

export const LoginSchema = yup.object().shape({
  email: yup
    .string()
    .email('Email must be a valid email')
    .required('Email is required'),
  password: yup
    .string()
    .required('Password is required')
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!$%^&()_+|~=`{}\[\]:";'<>?,.#@*\-/\\]).{8,}$/,
      'The password must be valid',
    ),
});

export const SignUpSchema = yup.object().shape({
  email: yup
    .string()
    .required('Email is required')
    .matches(
      /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
      'Must be valid email',
    ),
  phone: yup
    .string()
    .nullable()
    .test(
      'phone-length',
      'Phone number must be between 10 and 15 digits',
      (value) => {
        if (!value) return true; // If empty, it's optional
        return value.length >= 10 && value.length <= 15;
      },
    ),
  // terms: yup
  //   .boolean()
  //   .required(
  //     "Please review and accept our Terms of Service and Privacy Policy to continue."
  //   )
  //   .oneOf(
  //     [true],
  //     "Please review and accept our Terms of Service and Privacy Policy to continue."
  //   ),
});

export const NameSchema = yup.object().shape({
  firstName: yup
    .string()
    .required('First Name is required')
    .matches(/^[A-Za-z\s\-]+$/, 'First Name include only alphabetic characters')
    .min(3, 'Must be more than 3 characters')
    .max(20, 'Must be less than 20 characters'),
  lastName: yup
    .string()
    .required('Last Name is required')
    .matches(/^[A-Za-z\s\-]+$/, 'Last Name include only alphabetic characters')
    .min(3, 'Must be more than 3 characters')
    .max(20, 'Must be less than 20 characters'),
});

export const UserNameSchema = yup.object().shape({
  username: yup.string().required('Username is required'),
});

export const SelectPasswordSchema = yup.object().shape({
  password: yup
    .string()
    .required('Password is required')
    .min(8, 'Must be at least 8 characters')
    .matches(/\d/, 'Must contain a number')
    .matches(/[A-Z]/, 'Must contain an uppercase letter')
    .matches(/[!@#$%^&*]/, 'Must contain a special character'),
  confirmPassword: yup
    .string()
    .oneOf([yup.ref('password')], 'Passwords must match')
    .required('Confirm Password is required'),
});

export const ForgotSchema = yup.object().shape({
  email: yup
    .string()
    .email('Email must be a valid email')
    .required('Email is required'),
});

export const ResetPasswordSchema = yup.object().shape({
  code: yup
    .number()
    .required('OTP is required')
    .typeError('OTP must be a number')
    .min(100000, 'OTP must be exactly 6 digits')
    .max(999999, 'OTP must be exactly 6 digits'),
  password: yup
    .string()
    .required('Password is required')
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!$%^&()_+|~=`{}\[\]:";'<>?,.#@*\-/\\]).{8,}$/,
      'Minimum eight characters, at least one capital letter, one number and one special character',
    ),
  confirmPassword: yup
    .string()
    .required('Confirm Password is required')
    .oneOf([yup.ref('password')], 'Passwords must match'),
});

export const ProfileSchema = yup.object().shape({
  firstName: yup
    .string()
    .required('First Name is required')
    .matches(/^[A-Za-z\s\-]+$/, 'First Name include only alphabetic characters')
    .min(3, 'Must be more than 3 characters')
    .max(20, 'Must be less than 20 characters'),
  lastName: yup
    .string()
    .required('Last Name is required')
    .matches(/^[A-Za-z\s\-]+$/, 'Last Name include only alphabetic characters')
    .min(3, 'Must be more than 3 characters')
    .max(20, 'Must be less than 20 characters'),
  username: yup.string().required('Username is required'),
  // walletAddress: yup
  //   .string()
  //   .required('Wallet address is required')
  //   .matches(/^0x[a-fA-F0-9]{40}$/, 'Invalid wallet address format'),
  email: yup
    .string()
    .required('Email is required')
    .matches(
      /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
      'Must be valid email',
    ),
});

export const ArtistProfileSchema = yup.object().shape({
  firstName: yup
    .string()
    .required('First Name is required')
    .matches(/^[A-Za-z\s\-]+$/, 'First Name include only alphabetic characters')
    .min(3, 'Must be more than 3 characters')
    .max(20, 'Must be less than 20 characters'),
  lastName: yup
    .string()
    .required('Last Name is required')
    .matches(/^[A-Za-z\s\-]+$/, 'Last Name include only alphabetic characters')
    .min(3, 'Must be more than 3 characters')
    .max(20, 'Must be less than 20 characters'),
  username: yup.string().required('Username is required'),
  // walletAddress: yup
  //   .string()
  //   .required('Wallet address is required')
  //   .matches(/^0x[a-fA-F0-9]{40}$/, 'Invalid wallet address format'),
  email: yup
    .string()
    .required('Email is required')
    .matches(
      /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
      'Must be valid email',
    ),
});

export const CreateLibrarySchema = yup.object().shape({
  title: yup.string().required('Title is required'),
  collectionType: yup
    .object()
    .shape({
      value: yup.string().required(),
      label: yup.string().required(),
    })
    .nullable()
    .required('Collection type is required'),
  genre: yup
    .object()
    .shape({
      value: yup.string().required(),
      label: yup.string().required(),
    })
    .nullable()
    .required('Genre is required'),
  subscriptionEnabled: yup.boolean(),
});

export interface CreateTokenFormData {
  poolType: 'Standard Token' | 'Liquidity Generator Token';
  name: string;
  symbol: string;
  totalSupply: number;

  // Standard Token specific
  decimals?: number;

  // Liquidity Generator Token specific
  router?: {
    value: string;
    label: string;
  } | null;
  yieldFee?: number;
  liquidityFee?: number;
  charityAddress?: string;
  anotherFee?: number;
}

export const CreateTokenSchema = () =>
  yup.object().shape({
    // ...(poolType === 'Standard Token'
    // ? {
    name: yup
      .string()
      .matches(/^[A-Za-z]{1,7}$/, 'Only 1–7 letters allowed.')
      .required('Name is required'),
    symbol: yup.string().required('Symbol is required'),
    totalSupply: yup
      .number()
      .required('Total Supply is required')
      .typeError('Total Supply must be a number'),
    decimals: yup
      .number()
      .typeError('Decimals must be a number')
      .required('Decimals is required')
      .min(2, 'Token decimals must be greather than or equal to 2'),
    // logoUrl: yup
    //   .mixed<FileList>()
    //   .test(
    //     'fileList',
    //     'Invalid file',
    //     (value) => value === null || value instanceof File,
    //   )
    //   .nullable(),
    // }
    // : {
    //     router: yup.object().nullable().required('Router is required'),
    //     yieldFee: yup
    //       .number()
    //       .typeError('Yield fee must be a number')
    //       .required('Yield fee is required'),
    //     liquidityFee: yup
    //       .number()
    //       .typeError('Liquidity fee must be a number')
    //       .required('Liquidity fee is required'),
    //     charityAddress: yup
    //       .string()
    //       .required('Charity/Marketing address is required'),
    //     anotherFee: yup
    //       .number()
    //       .typeError('Another fee must be a number')
    //       .required('Another fee is required'),
    //   }),
  });

export const timingSchema = yup.object().shape({
  presaleStart: yup
    .date()
    .required('Presale start time is required')
    .typeError('Presale start time must be a valid date'),
  presaleEnd: yup
    .date()
    .required('Presale end time is required')
    .typeError('Presale end time must be a valid date')
    .min(yup.ref('presaleStart'), 'End time must be after start time'),
  liquidityUnlock: yup
    .date()
    .required('Liquidity unlock time is required')
    .typeError('Liquidity unlock must be a valid date')
    .min(yup.ref('presaleEnd'), 'Liquidity unlock must be after presale end'),
});

export const fairLaunchSchema = yup.object().shape({
  fairLaunchAmount: yup
    .number()
    .typeError('Fairlaunch Amount must be a number')
    .required('Fairlaunch Amount is required'),

  listingOn: yup.string().required('Listing Amount is required'),

  softCap: yup
    .number()
    .typeError('Soft Cap must be a number')
    .required('Soft Cap is required')
    .positive('Soft Cap must be greater than 0'),

  hardCap: yup
    .number()
    .typeError('Hard Cap must be a number')
    .required('Hard Cap is required')
    .positive('Hard Cap must be greater than 0'),

  // minBuy: yup
  //   .number()
  //   .typeError('Min Contribution must be a number')
  //   .required('Min Contribution is required')
  //   .positive('Min Contribution must be greater than 0'),

  // maxBuy: yup
  //   .number()
  //   .typeError('Max Contribution must be a number')
  //   .required('Max Contribution is required')
  //   .moreThan(yup.ref('minBuy'), 'Max must be greater than Min'),
});

export const TrackSchema = yup.object().shape({
  title: yup.string().required('Track title is required'),
  artist: yup.string().required('Artist name is required'),
  description: yup.string().nullable(),
  genre: yup.object().nullable().shape({
    value: yup.string().required(),
    label: yup.string().required(),
  }),
  privacy: yup.string().oneOf(['public', 'private']).required(),
  isFree: yup.boolean(),
});
