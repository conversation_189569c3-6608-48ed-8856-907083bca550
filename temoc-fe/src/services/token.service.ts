import { HttpService } from '@/services/base.service';
import { PresaleTokenRes } from '@/types/token.interface';

class TokenService extends HttpService {
  private readonly prefix: string = 'token';

  /**
   * Add a new social link
   * @param data
   */
  createToken = (data: any): Promise<any> => {
    return this.post(`${this.prefix}/create`, data);
  };

  /**
   * Add a new social link
   * @param data
   */
  getToken = (): Promise<any> => {
    return this.get(`${this.prefix}/my-tokens`);
  };

  /**
   * Add a new social link
   * @param data
   */
  getArtistToken = (userId: string): Promise<any> => {
    return this.get(`${this.prefix}/artist-tokens/${userId}`);
  };

  /**
   * Add a new social link
   * @param data
   */
  getLaunchedToken = (params: any): Promise<PresaleTokenRes> => {
    return this.get(`${this.prefix}/launched`, params);
  };

  /**
   * Add a new social link
   * @param data
   */
  getPresaleToken = (params: any): Promise<PresaleTokenRes> => {
    return this.get(`${this.prefix}/findAll`, params);
  };
}

export const tokenService = new TokenService();
