import {
  IForgot,
  ILogin,
  ILoginResponse,
  IResendCode,
  IReset,
  ISignUpResponse,
  ISingUp,
  IVerifyEmail,
} from '@/types/auth.interface';
import { HttpService } from './base.service';
// import * as Cookies from 'js-cookie';

class AuthService extends HttpService {
  private readonly prefix: string = 'auth';

  /**
   * Sign in
   * @param data
   */
  login = (data: ILogin): Promise<ILoginResponse> => {
    // @ts-ignore
    return this.post(`${this.prefix}/login`, data);
  };

  /**
   * Get ALL trends
   * @param data
   */
  signup = (data: ISingUp): Promise<ISignUpResponse> => {
    // @ts-ignore
    return this.post(`${this.prefix}/signup`, data);
  };

  /**
   * Get ALL trends
   * @param data
   */
  verifyEmail = (data: IVerifyEmail): Promise<any> => {
    return this.post(`${this.prefix}/verify-email`, data);
  };

  /**
   * Get ALL trends
   * @param data
   */
  resendCode = (data: IResendCode): Promise<any> => {
    return this.post(`${this.prefix}/re-send-code`, data);
  };

  /**
   * Get ALL trends
   * @param data
   */
  forgotPassword = (data: IForgot): Promise<any> => {
    return this.post(`${this.prefix}/send-password-reset`, data);
  };

  /**
   * Get ALL trends
   * @param data
   */
  resetPassword = (data: IReset): Promise<any> => {
    return this.post(`${this.prefix}/reset-password`, data);
  };

  updateProfile = (data: any): Promise<any> => {
    return this.put(`${this.prefix}/update-profile`, data);
  };

  /**
   * Verify invitation code
   * @param invitationCode
   */
  verifyInvitationCode = (invitationCode: string): Promise<any> => {
    return this.post(`users/verify-invitation`, { invitationCode });
  };

  /**
   * Sign in
   * @param data
   */
  getUser = async (): Promise<any> => {
    try {
      return await this.get(`${this.prefix}/me`);
    } catch (error: any) {
      console.log('404 me', error);
      if (
        error?.code === 'ERR_NETWORK' ||
        error?.response?.status === 500 ||
        error?.response?.status === 404
      ) {
        // localStorage.removeItem('dynamic_authentication_token');
        // Cookies.default.remove('dynamic_authentication_token');
        // window.location.href = `http://localhost:3000/`;
      }
      throw error;
    }
  };
}
export const authService = new AuthService();
