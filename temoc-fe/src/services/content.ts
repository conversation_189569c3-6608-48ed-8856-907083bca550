import { api } from './api';

export interface Library {
  _id: string;
  title: string;
  description?: string;
  thumbnailUrl?: string;
  requiresSubscription: boolean;
  creator: {
    _id: string;
    firstName: string;
    lastName: string;
    username: string;
    avatarUrl?: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface Album {
  _id: string;
  title: string;
  description?: string;
  thumbnailUrl?: string;
  library: string;
  requiresTokens: boolean;
  creator: {
    _id: string;
    firstName: string;
    lastName: string;
    username: string;
    avatarUrl?: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface Track {
  _id: string;
  title: string;
  description?: string;
  fileUrl: string;
  thumbnailUrl?: string;
  album: string | { _id: string; title: string }; // Can be populated or just ID
  privacy: 'PUBLIC' | 'PRIVATE' | 'UNLISTED';
  playCount: number;
  creator: {
    _id: string;
    firstName: string;
    lastName: string;
    username: string;
    avatarUrl?: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface LibrariesResponse {
  success: boolean;
  libraries: Library[];
}

export interface AlbumsResponse {
  success: boolean;
  albums: Album[];
}

export interface TracksResponse {
  success: boolean;
  tracks: Track[];
}

interface Creator {
  _id: string;
  displayName: string;
  avatarUrl: string;
}

interface Albums {
  _id: string;
  title: string;
}

interface Metadata {
  fileType: string;
  fileSize: number;
  originalName: string;
  publicId: string;
}

export interface TrendingTrack {
  _id: string;
  title: string;
  description: string;
  artist: string;
  genre: string;
  tags: string[];
  fileUrl: string;
  thumbnailUrl: string;
  duration: number;
  privacy: string;
  playCount: number;
  creator: Creator;
  album: Albums;
  metadata: Metadata;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

export type TrendingSongRes = TrendingTrack[];

export const contentApi = {
  // Get public libraries (for all users to see)
  getPublicLibraries: async (): Promise<LibrariesResponse> => {
    const response = await api.get('/content/public/libraries');
    return response.data as LibrariesResponse;
  },

  // Get public albums in a library
  getPublicAlbums: async (libraryId: string): Promise<AlbumsResponse> => {
    const response = await api.get(
      `/content/public/libraries/${libraryId}/albums`,
    );
    return response.data as AlbumsResponse;
  },

  // Get public tracks in an album
  getPublicTracks: async (albumId: string): Promise<TracksResponse> => {
    const response = await api.get(`/content/public/albums/${albumId}/tracks`);
    return response.data as TracksResponse;
  },

  // Record track play
  recordTrackPlay: async (trackId: string): Promise<{ success: boolean }> => {
    const response = await api.post(`/content/tracks/${trackId}/play`);
    return response.data as { success: boolean };
  },

  // Get libraries by user (for profile content tab)
  getLibrariesByUser: async (userId: string): Promise<LibrariesResponse> => {
    console.log('🎵 [ContentService] Fetching libraries for user:', userId);
    try {
      const response = await api.get(
        `/content/public/libraries/user/${userId}`,
      );
      console.log(
        '🎵 [ContentService] User libraries response:',
        response.data,
      );
      console.log(
        '🎵 [ContentService] Number of libraries found:',
        (response.data as LibrariesResponse).libraries?.length || 0,
      );
      return response.data as LibrariesResponse;
    } catch (error) {
      console.error(
        '🎵 [ContentService] Error fetching user libraries:',
        error,
      );
      throw error;
    }
  },

  // Get albums by user with track counts (for profile content overview)
  getAlbumsByUser: async (userId: string): Promise<AlbumsResponse> => {
    console.log(
      '🎵 [ContentService] Fetching albums with track counts for user:',
      userId,
    );
    try {
      const response = await api.get(`/content/public/albums/user/${userId}`);
      console.log('🎵 [ContentService] User albums response:', response.data);
      console.log(
        '🎵 [ContentService] Number of albums found:',
        (response.data as AlbumsResponse).albums?.length || 0,
      );
      return response.data as AlbumsResponse;
    } catch (error) {
      console.error('🎵 [ContentService] Error fetching user albums:', error);
      throw error;
    }
  },

  // Get tracks by user (for profile content overview)
  getTracksByUser: async (userId: string): Promise<TracksResponse> => {
    console.log('🎵 [ContentService] Fetching tracks for user:', userId);
    try {
      const response = await api.get(`/content/public/tracks/user/${userId}`);
      console.log('🎵 [ContentService] User tracks response:', response.data);
      console.log(
        '🎵 [ContentService] Number of tracks found:',
        (response.data as TracksResponse).tracks?.length || 0,
      );
      return response.data as TracksResponse;
    } catch (error) {
      console.error('🎵 [ContentService] Error fetching user tracks:', error);
      throw error;
    }
  },

  getTrendingSongs: async (): Promise<TrendingSongRes> => {
    const response = await api.get(`/content/trending-tracks`);
    return response.data as TrendingSongRes;
  },
};
