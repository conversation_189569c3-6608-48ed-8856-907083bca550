import { useState, createContext, FC, useEffect, useCallback } from 'react';
import { DefaultChain, supportedChains } from '@/utils/chains';

const supportedChainsMap = supportedChains.reduce(
  (map, chain) => {
    map[chain.id] = chain;
    return map;
  },
  {} as Record<string, (typeof supportedChains)[0]>,
);

export const ChainContext = createContext<{
  chain: typeof DefaultChain;
  switchCurrentChain: (chainId: string | number) => void;
}>({
  chain: DefaultChain,
  switchCurrentChain: () => {},
});

const ChainContextProvider: FC<any> = ({ children }) => {
  const [globalChainId, setGlobalChainId] = useState<number>(DefaultChain.id);

  useEffect(() => {
    const savedChainId: number = Number(
      localStorage.getItem('offload.chainId') || DefaultChain.id,
    );
    let selectedChain: (typeof supportedChains)[0] | undefined;

    if (!selectedChain) {
      selectedChain = supportedChains.find(
        (chain) => chain.id === +savedChainId,
      );
    }

    const id = selectedChain?.id || DefaultChain.id;
    setGlobalChainId(id);
    localStorage.setItem('offload.chainId', `${id}`);
  }, []);

  const switchCurrentChain = useCallback(
    async (chainId: string | number) => {
      const numericChainId = Number(chainId);
      if (numericChainId === globalChainId) return;

      const chain = supportedChainsMap[numericChainId];
      if (!chain) return;

      if (typeof window === 'undefined' || !window.ethereum) {
        console.warn('No Ethereum provider found');
        return;
      }

      try {
        await window.ethereum.request({
          method: 'wallet_switchEthereumChain',
          params: [{ chainId: `0x${numericChainId.toString(16)}` }],
        });
      } catch (switchError: any) {
        if (switchError.code === 4902) {
          try {
            await window.ethereum.request({
              method: 'wallet_addEthereumChain',
              params: [
                {
                  chainId: `0x${numericChainId.toString(16)}`,
                  chainName: chain.name,
                  rpcUrls: chain.rpcUrls,
                  nativeCurrency: chain.nativeCurrency,
                  blockExplorerUrls: chain.blockExplorers.default.url,
                },
              ],
            });
          } catch (addError) {
            console.error('Failed to add chain:', addError);
            return;
          }
        } else {
          console.error('Failed to switch chain:', switchError);
          return;
        }
      }

      setGlobalChainId(numericChainId);
      localStorage.setItem('offload.chainId', `${numericChainId}`);
    },
    [globalChainId],
  );

  let currentChain = DefaultChain;
  if (globalChainId && supportedChainsMap[globalChainId]) {
    //@ts-ignore
    currentChain = supportedChainsMap[globalChainId];
  }

  return (
    <ChainContext.Provider value={{ chain: currentChain, switchCurrentChain }}>
      {children}
    </ChainContext.Provider>
  );
};

export default ChainContextProvider;
