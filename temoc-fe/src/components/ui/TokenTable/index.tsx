import React from 'react';
import clsx from 'clsx';
import NoData from '../NoData';
import NoCollection from '@/components/common/Icons/NoCollection';
import PaginationNew from '../PaginationNew';
import Loader from '@/components/common/Loader';

type Column = {
  key: string;
  label: React.ReactNode;
};

type RowData = {
  [key: string]: string | number | React.ReactNode;
};

type TokenTableProps = {
  columns: Column[];
  data: RowData[];
  bg?: any;
  className?: string;
  headClass?: string;
  bodyClass?: string;
  loading?: boolean; // Add loading prop to control loading state
  isPlaying?: boolean;
  currentTrackUrl?: string | null;
  onPageChange?: (page: number) => void;
  initialPage?: number; // Initial page number
  itemsPerPage?: number; // Number of items per page
  totalPages?: number; // Number of items per page
  presale?: boolean; // Optional prop to indicate if it's a presale table
};

const TokenTable: React.FC<TokenTableProps> = ({
  columns,
  data,
  bg,
  loading,
  className,
  headClass,
  initialPage = 1,
  totalPages = 0,
  onPageChange,
  presale,
  bodyClass,
  isPlaying,
  currentTrackUrl,
}) => {
  const handlePageChange = (page: number) => {
    onPageChange?.(page);
  };

  console.log(data, 'data in token table');

  return (
    <div
      className={`overflow-x-auto ${className} rounded-xl ${bg ? 'shadow-[0_4px_10px_rgba(0,0,0,0.08)]' : ''} `}
    >
      <table className="min-w-full text-left text-sm">
        <thead className="">
          <tr className="">
            {columns.map((col) => (
              <th
                key={col.key}
                className={`border border-b border-l-0 border-r-0 border-t-0 px-6 py-3 text-base font-normal text-[#333333] ${headClass}`}
              >
                {col.label}
              </th>
            ))}
          </tr>
        </thead>
        <tbody
          className={`${bg ? 'shadow-[0_4px_10px_rgba(0,0,0,0.08)]' : ''}`}
        >
          {loading && (
            <tr>
              <td colSpan={columns.length} className={`px-5 py-7 text-center`}>
                <div className="flex h-[30vh] items-center justify-center py-6 text-black">
                  <Loader />
                </div>
              </td>
            </tr>
          )}
          {data.length === 0 && !loading ? (
            <tr>
              {presale ? (
                <td
                  colSpan={columns.length}
                  className={`px-5 py-7 text-center`}
                >
                  <div className="flex h-[30vh] items-center justify-center py-6 text-black">
                    {'No Data Available'}
                  </div>
                </td>
              ) : (
                <td colSpan={columns.length}>
                  <NoData
                    icon={<NoCollection />}
                    heading="No songs found"
                    description="You haven’t added any songs yet. Upload your first track to share your music with the world and start building your collection."
                  />
                </td>
              )}
            </tr>
          ) : (
            data.map((row, idx) => (
              <tr
                key={idx}
                className={clsx(
                  'px-5 hover:bg-gray-200',
                  currentTrackUrl === row?.trackUrl &&
                    (isPlaying ? 'bg-gray-200' : 'bg-gray-50'),
                )}
                // className={`hover:bg-gray-200 ${isPlaying && currentTrackUrl === row?.trackUrl && 'bg-gray-200'}`}
              >
                {columns.map((col) => {
                  const value = row[col.key];
                  return (
                    <td
                      key={col.key}
                      className={clsx(
                        'whitespace-nowrap px-5 py-3 text-base text-black-300',
                        col.key === 'type' &&
                          value === 'Buy' &&
                          'text-green-500',
                        col.key === 'type' &&
                          value === 'Sell' &&
                          'text-red-500',
                        bodyClass,
                      )}
                    >
                      {value}
                    </td>
                  );
                })}
              </tr>
            ))
          )}
        </tbody>
      </table>
      {totalPages > 1 && (
        <div className="mt-8 flex w-full justify-center">
          <PaginationNew
            currentPage={initialPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
          />
        </div>
      )}
    </div>
  );
};

export default TokenTable;
