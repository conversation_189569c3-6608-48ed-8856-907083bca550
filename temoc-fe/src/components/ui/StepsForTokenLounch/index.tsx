import Loader from '@/components/common/Loader';
import Modal from '@/components/common/Modal';
import { CheckCircle, Loader2 } from 'lucide-react'; // Icons
import React from 'react';

interface Step {
  title: string;
  description: string;
}

interface Iprops {
  setOpenModal: (open: boolean) => void;
  openModal: boolean;
  steps: Step[];
  currentStepIndex: number;
}

const StepsForTokenLounch = ({
  setOpenModal,
  openModal,
  steps = [],
  currentStepIndex,
}: Iprops) => {
  return (
    <Modal
      dontClose={true}
      className="hideScrollbar !h-max !max-w-[600px]"
      show={openModal}
      hide={setOpenModal}
    >
      <div className="flex flex-col gap-6 p-6 text-left">
        {steps.map((step, index) => {
          const isCompleted = index < currentStepIndex;
          const isCurrent = index === currentStepIndex;

          return (
            <div key={index} className="flex items-start gap-4">
              {isCompleted ? (
                <CheckCircle className="mt-1 text-green-500" size={24} />
              ) : isCurrent ? (
                <Loader2 className="mt-1 animate-spin text-primary" size={24} />
              ) : (
                <div className="mt-1 h-6 w-6 rounded-full border-2 border-gray-300" />
              )}

              <div>
                <h3
                  className={`text-lg font-semibold ${
                    isCompleted
                      ? 'text-green-600'
                      : isCurrent
                        ? 'text-primary'
                        : 'text-gray-600'
                  }`}
                >
                  {step.title}
                </h3>
                <p className="text-sm text-gray-500">{step.description}</p>
              </div>
            </div>
          );
        })}

        <div className="flex justify-center pt-4">
          <Loader loading={true} size={20} />
        </div>
      </div>
    </Modal>
  );
};

export default StepsForTokenLounch;
