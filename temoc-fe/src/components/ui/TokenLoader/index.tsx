import Loader from '@/components/common/Loader';
import Modal from '@/components/common/Modal';
import React from 'react';
interface Iprops {
  setOpenModal: any;
  openModal: any;
  desc?: string;
  title?: string;
}
const TokenLoader = ({ setOpenModal, openModal, desc, title }: Iprops) => {
  return (
    <Modal
      className="hideScrollbar !h-max !max-w-[600px]"
      show={openModal}
      hide={() => setOpenModal(false)}
    >
      <div className="mb-pb-8 flex flex-col items-center justify-center gap-4 pb-5 text-center">
        <Loader loading={true} size={50} />
        <h2 className="mt-8 !font-display !text-2xl text-black sm:!text-3xl">
          {title ? title : ' Please wait'}
        </h2>
        <p className="text-xl text-black">
          {desc ? desc : 'We are initializing the payment widget.'}
        </p>
      </div>
    </Modal>
  );
};

export default TokenLoader;
