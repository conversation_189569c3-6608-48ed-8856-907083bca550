import ImageComponent from '@/components/common/ImageComponent';
import React from 'react';
import { FaPlay } from 'react-icons/fa'; // You can use any icon library

interface CardProps {
  imageSrc?: string;
  name?: string;
  followers?: string;
  profileSrc?: string;
  artistName?: string;
  playIcon?: boolean;
}

const Card: React.FC<CardProps> = ({
  imageSrc,
  name,
  followers,
  profileSrc,
  artistName,
  playIcon,
}) => {
  return (
    <div className="group relative w-full rounded-[10px] p-2 transition-all duration-300 hover:bg-white hover:shadow-[0_5px_10px_rgba(0,0,0,0.05)] xl:p-4">
      <div className="relative overflow-hidden rounded-md pb-[100%]">
        <img
          src={imageSrc}
          alt={'card'}
          className="absolute inset-0 h-full w-full object-cover transition-transform duration-300 group-hover:scale-110"
        />
        {!playIcon && (
          <div className="absolute -bottom-8 right-2 z-30 flex h-12 w-12 items-center justify-center rounded-full bg-primary opacity-0 transition-[bottom] duration-300 group-hover:bottom-2 group-hover:opacity-100">
            <FaPlay className="text-lg text-white" />
          </div>
        )}
      </div>

      <div className="mt-1 sm:mt-4">
        <p className="!text-[10px] !font-medium md:!text-xs xl:!text-sm">
          {name}
        </p>
        <div
          className={`${
            profileSrc && 'xl:gap-2'
          } flex w-full items-center sm:mt-2`}
        >
          {profileSrc && (
            <ImageComponent
              src={profileSrc}
              figClassName="sm:h-[20px] h-2.5 w-2.5 sm:w-[20px] flex-shrink-0"
              className="rounded-full object-cover"
              fill
            />
          )}
          {artistName && (
            <p className="w-[80%] truncate !text-[10px] font-normal !text-[#666666] xl:!text-xs">
              {artistName}
            </p>
          )}
          {followers && (
            <p className="w-[80%] truncate !text-[10px] font-normal !text-[#666666] xl:!text-xs">
              {followers}
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default Card;
