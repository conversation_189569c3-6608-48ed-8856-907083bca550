'use client';
import Button from '@/components/common/Button';
import { Input } from '@/components/common/Forms/Input';
import InputError from '@/components/common/Forms/InputError';
import RadioButton from '@/components/common/Forms/RadioButton';
import SelectComponent from '@/components/common/Forms/Select';
import Arrow from '@/components/common/Icons/Arrow';
import { CreateTokenFormData } from '@/types/token.interface';
import { CreateTokenSchema } from '@/utils/schema';
import { yupResolver } from '@hookform/resolvers/yup';
import React, { useContext, useEffect, useMemo, useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import { ethers } from 'ethers';
import { TEMOC_TOKEN_ABI } from '@/lib/web3/abi/TEMOC_TOKEN_ABI';
import { useEthersSigner } from '@/hooks/useEthersSigner';
import { toast } from 'react-toastify';
import { DynamicWidget, useDynamicContext } from '@dynamic-labs/sdk-react-core';
import { TEMOC_TOKEN_BYTECODE } from '@/lib/web3/byteCode/TEMOC_TOKEN_BYTECODE';
import { ChainContext } from '@/context/ChainContextProvider';
import Upload from '@/components/common/Icons/Upload';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { tokenService } from '@/services/token.service';
// import Tooltip from '@/components/common/Tooltip';
import { BsInfoCircle } from 'react-icons/bs';
import TokenLoader from '../TokenLoader';
import { sepolia } from 'viem/chains';

interface Iprops {
  setOpenModal: React.Dispatch<React.SetStateAction<boolean>>;
  setTokenAddress: React.Dispatch<React.SetStateAction<string | null>>;
  setLogo: React.Dispatch<React.SetStateAction<string | null>>;
}
const CreateTokenModal = ({
  setOpenModal,
  setTokenAddress,
  setLogo,
}: Iprops) => {
  const [poolType, setPoolType] = useState<string>('Standard Token');
  const [loading, setLoading] = useState<boolean>(false);
  const [fileName] = useState<string | null>(null);
  const inputRef = useRef<HTMLInputElement | null>(null);
  const { primaryWallet, setShowAuthFlow } = useDynamicContext();
  const [logoPreviewUrl, setLogoPreviewUrl] = useState<string | null>(null);
  const { chain } = useContext(ChainContext);
  const queryClient = useQueryClient();
  const signer = useEthersSigner({ chainId: chain.id });
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [selectChain, setSelectChain] = useState<{
    value: string;
    label: string;
  } | null>(null);
  const chainArray = [
    {
      label: (
        <p className="flex items-center gap-1 text-base font-normal text-[#181818]">
          Ethereum
        </p>
      ),
      value: 'ethereum',
    },
    {
      label: (
        <p className="flex items-center gap-1 text-base font-normal text-[#181818]">
          Base
        </p>
      ),
      value: 'base',
    },
    {
      label: (
        <p className="flex items-center gap-1 text-base font-normal text-[#181818]">
          Solana
        </p>
      ),
      value: 'solana',
    },
    {
      label: (
        <p className="flex items-center gap-1 text-base font-normal text-[#181818]">
          TON
        </p>
      ),
      value: 'ton',
    },
  ];

  const validationSchema = useMemo(() => CreateTokenSchema(), [poolType]);
  const { switchCurrentChain } = useContext(ChainContext);

  const {
    handleSubmit,
    register,
    watch,
    formState: { errors },
  } = useForm<CreateTokenFormData>({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      decimals: 18,
    },
  });

  const tokenMutation = useMutation({
    mutationFn: (data: any) => tokenService.createToken(data),
    onSuccess: (data) => {
      setLogo(data?.data?.logoUrl);
      toast.success('Token created successfully');
      queryClient.invalidateQueries({ queryKey: ['token'] });
    },
    onError: (data) => {
      console.log(data, 'presale error');
      toast.error(data?.message || 'Something went wrong while creating token');
    },
  });

  const deployContract = async (data: any) => {
    try {
      if (!primaryWallet?.isConnected) {
        setTitle('Wallet not connected');
        setDescription(
          'Please connect your wallet to proceed with deployment.',
        );

        setShowAuthFlow(true);
        return null;
      }

      setLoading(true);

      const name = data?.name;
      const symbol = data?.symbol;
      const decimals = data?.decimals || 18;
      const totalSupply = data?.totalSupply || 0;

      setTitle(`Deploying ${name} (${symbol})...`);
      setDescription(
        `We are deploying your token with total supply of ${totalSupply} and ${decimals} decimals.`,
      );

      const contractFactory = new ethers.ContractFactory(
        TEMOC_TOKEN_ABI,
        TEMOC_TOKEN_BYTECODE,
        signer,
      );

      const initialOwner = primaryWallet.address;

      const contract = await contractFactory.deploy(
        initialOwner,
        name,
        symbol,
        decimals,
        totalSupply,
      );

      await contract.waitForDeployment();

      const address = await contract.getAddress();

      setTitle(`Deployment successful!`);
      setDescription(`Your token contract was deployed to address: ${address}`);

      setLoading(false);
      return address;
    } catch (err: any) {
      setLoading(false);
      setTitle('Deployment failed');

      if (
        err?.code === 4001 ||
        err?.message?.toLowerCase().includes('user rejected')
      ) {
        setDescription('You cancelled the transaction in your wallet.');
        toast.info('Transaction cancelled by user');
      } else {
        setDescription(
          err.message || 'Something went wrong during deployment.',
        );
        toast.error(err.message || 'Something went wrong');
      }

      return null;
    }
  };

  const logoUrlValue = watch('logoUrl');
  const logoFile = Array.isArray(logoUrlValue)
    ? logoUrlValue[0]
    : logoUrlValue instanceof FileList
      ? logoUrlValue[0]
      : undefined;

  useEffect(() => {
    if (logoFile && logoFile instanceof File) {
      const fileUrl = URL.createObjectURL(logoFile);
      setLogoPreviewUrl(fileUrl);

      return () => URL.revokeObjectURL(fileUrl); // Clean up memory
    } else {
      setLogoPreviewUrl(null);
    }
  }, [logoFile]);

  const onSubmit = async (data: any) => {
    if (primaryWallet) {
      switchCurrentChain(sepolia.id);
      await primaryWallet.switchNetwork(sepolia.id);
      await new Promise((resolve) => setTimeout(resolve, 300));
    }
    const file = data.logoUrl?.[0];
    await deployContract(data).then(async (address: any) => {
      if (address) {
        const formData = new FormData();
        formData.append('name', data.name);
        formData.append('symbol', data.symbol);
        formData.append('address', address);
        formData.append('walletAddress', primaryWallet?.address || '');
        formData.append('decimals', (data.decimals || 18).toString());
        formData.append('totalSupply', data.totalSupply.toString());
        formData.append('network', chain?.name || 'Base Sepolia');
        if (file) {
          formData.append('logo', file);
        }
        await tokenMutation.mutateAsync(formData);
        setTokenAddress(address);
        setOpenModal(false);
      }
    });
  };
  const [loadingModal, setLoadingModal] = useState(false);
  useEffect(() => {
    if (loading || tokenMutation.isPending) {
      setLoadingModal(true);
    } else {
      setLoadingModal(false);
    }
  }, [tokenMutation, loading]);

  return (
    <>
      <form onSubmit={handleSubmit(onSubmit)} className="text-center">
        <h3 className="text-center text-2xl font-semibold uppercase text-[#333333] sm:text-[30px]">
          Create Token
        </h3>

        <div className="mt-5 flex flex-wrap items-center justify-center gap-3 sm:flex-nowrap sm:gap-5">
          {/* <p className="text-sm">Type</p> */}
          <RadioButton
            label="Standard Token"
            value="Standard Token"
            name="tokenType"
            checked={poolType === 'Standard Token'}
            onChange={() => setPoolType('Standard Token')}
          />
          {/* <RadioButton
          label="Liquidity Generator Token"
          value="Liquidity Generator Token"
          name="tokenType"
          checked={poolType === 'Liquidity Generator Token'}
          onChange={() => setPoolType('Liquidity Generator Token')}
        /> */}
        </div>

        <div className="mt-5 flex flex-wrap justify-center gap-2.5 xs:mt-3">
          <DynamicWidget
            variant="modal"
            innerButtonComponent={
              <Button className="AtConnect">Connect Wallet</Button>
            }
            buttonClassName="!bg-[#C9FA49] !text-[#333333] font-bold rounded-full"
          />
        </div>
        <div className="mt-2 flex flex-col items-center justify-center">
          <label
            className="flex w-[220px] cursor-pointer flex-col items-center justify-center gap-2 rounded-[14px] border border-[#CECECE] p-3 transition hover:border-primary"
            htmlFor="logoFile"
          >
            {logoPreviewUrl ? (
              <img
                src={logoPreviewUrl}
                alt="Logo Preview"
                className="h-28 w-40 rounded-2xl object-cover"
              />
            ) : (
              <div className="flex h-28 flex-col items-center justify-center">
                {/* <h3 className="max-w-[170px] truncate text-center text-xs text-[#777777]">
                Add Logo (optional)
              </h3> */}
                <Upload />
                <p className="mt-1 text-center text-xs text-[#666666]">
                  {fileName || 'Upload Logo'}
                </p>
              </div>
            )}
          </label>
          <input
            id="logoFile"
            type="file"
            accept="image/*"
            {...register('logoUrl')}
            className="hidden"
            ref={(e) => {
              register('logoUrl').ref(e);
              inputRef.current = e;
            }}
          />

          <InputError error={errors.logoUrl?.message} />
        </div>

        {/* {poolType === 'Standard Token' && ( */}
        <div className="sm:gapy-4 mt-5 grid gap-4 text-left sm:grid-cols-2 sm:gap-x-8">
          <div className="">
            <p className="mb-1">Token Name</p>
            <Input
              name="name"
              placeholder="e.g., TEMOC"
              type="text"
              className=""
              register={register}
            />
            <InputError error={errors.name?.message} />
            <span className="mt-2 flex items-start gap-1 px-1 text-xs !leading-[15px] text-[#666666]">
              {' '}
              <BsInfoCircle className="flex-shrink-0 text-[#666666]" />
              The full name of your token (e.g., &quot;My Awesome Token&quot;).
            </span>
          </div>

          <div>
            <p className="mb-1">Token symbol</p>
            <Input
              name="symbol"
              placeholder="e.g., TEMOC"
              type="text"
              className=""
              register={register}
            />
            <InputError error={errors.symbol?.message} />
            <span className="mt-2 flex items-start gap-1 px-1 text-xs !leading-[15px] text-[#666666]">
              {' '}
              <BsInfoCircle className="flex-shrink-0 text-[#666666]" />
              The short ticker symbol for your token (e.g., &quot;MAT&quot;).
            </span>
          </div>

          {poolType === 'Standard Token' ? (
            <div className="">
              <div className="relative">
                <p className="mb-1">Token Decimals</p>
                <Input
                  name="decimals"
                  placeholder="18 Decimals"
                  type="number"
                  defaultValue={18}
                  className=""
                  register={register}
                />
                <InputError error={errors.decimals?.message} />
                {/* <Tooltip description=" We recommend using 18 decimals when creating a token." /> */}
              </div>
              <span className="mt-2 flex items-start gap-1 px-1 text-xs !leading-[15px] text-[#666666]">
                {' '}
                <BsInfoCircle className="flex-shrink-0 text-[#666666]" />
                The number of decimal places your token will have. Must be a
                positive number greater than or equal to 2. (Typically 18 for
                most EVM chains).
              </span>
            </div>
          ) : (
            <div className="">
              <SelectComponent
                selected={selectChain}
                onSelect={setSelectChain}
                options={chainArray}
                className="w-full"
                placeholder="Router"
              />
            </div>
          )}

          <div>
            <p className="mb-1">Total Supply</p>
            <Input
              name="totalSupply"
              placeholder="Total Supply"
              type="number"
              className=""
              register={register}
            />
            <InputError error={errors.totalSupply?.message} />
            <span className="mt-2 flex items-start gap-1 px-1 text-xs !leading-[15px] text-[#666666]">
              {' '}
              <BsInfoCircle className="flex-shrink-0 text-[#666666]" />
              The total number of tokens that will ever exist. Must be a
              positive number.
            </span>
          </div>
        </div>
        {/* )} */}

        {poolType === 'Liquidity Generator Token' && (
          <div className="mt-3 grid gap-3 text-left sm:grid-cols-2">
            <div>
              <p className="mb-1">Transaction fee to generate yield</p>
              <Input
                name="yieldFee"
                placeholder="Transaction fee to generate yield %"
                type="number"
                register={register}
                className=""
              />
              {/* <InputError error={errors.yieldFee?.message} /> */}
            </div>

            <div>
              <p className="mb-1">Transaction fee to generate liquidity</p>
              <Input
                name="liquidityFee"
                placeholder="Transaction fee to generate liquidity %"
                type="number"
                register={register}
                className=""
              />
              {/* <InputError error={errors.liquidityFee?.message} /> */}
            </div>

            <div>
              <p className="mb-1">Charity/Marketing Address</p>
              <Input
                name="charityAddress"
                placeholder="Charity/Marketing address"
                type="text"
                register={register}
                className=""
              />
              {/* <InputError error={errors.charityAddress?.message} /> */}
            </div>

            <div>
              <p className="mb-1">Another liquidity fee</p>
              <Input
                name="anotherFee"
                placeholder="Another liquidity fee %"
                type="number"
                register={register}
                className=""
              />
              {/* <InputError error={errors.anotherFee?.message} /> */}
            </div>
          </div>
        )}
        <div className="mt-5 flex w-full justify-between">
          <Button
            className="flex !gap-4 !border-none !bg-black-900 py-5 uppercase sm:!px-[60px]"
            onClick={() => setOpenModal(false)}
          >
            <Arrow className="rotate-180" />
            Back
          </Button>
          <Button
            type="submit"
            className="flex !gap-4 !border-none !py-5 uppercase sm:!px-[60px]"
            disabled={loading || tokenMutation.isPending}
            isLoading={loading || tokenMutation.isPending}
          >
            CREATE TOKEN
            <Arrow />
          </Button>
        </div>
      </form>
      <TokenLoader
        setOpenModal={setLoadingModal}
        openModal={loadingModal}
        title={title}
        desc={description}
      />
    </>
  );
};

export default CreateTokenModal;
