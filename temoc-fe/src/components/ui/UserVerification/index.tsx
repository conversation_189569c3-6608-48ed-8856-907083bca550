import { Button } from '@/components/common';
import ImageComponent from '@/components/common/ImageComponent';
import React, { useState } from 'react';
import <PERSON>n<PERSON>ield from 'react-pin-field';
import { toast } from 'react-toastify';
import { authService } from '@/services/auth.service';
import { useMutation } from '@tanstack/react-query';

interface UserVerificationProps {
  onVerificationSuccess: () => void;
}

const UserVerification = ({ onVerificationSuccess }: UserVerificationProps) => {
  const [pin, setPin] = useState('');
  const [isFilled, setIsFilled] = useState(false);

  const handlePinChange = (value: any) => {
    setPin(value);
    setIsFilled(value.length > 0);
  };

  const verifyInvitationMutation = useMutation({
    mutationFn: authService.verifyInvitationCode,
    onSuccess: (data) => {
      console.log('API Response:', data);
      if (data?.data?.success || data?.success) {
        toast.success('Invitation code verified successfully!');
        // Store verification status in localStorage
        localStorage.setItem('invitation_verified', 'true');
        onVerificationSuccess();
      } else {
        toast.error(
          data?.data?.message || data?.message || 'Invalid invitation code',
        );
      }
    },
    onError: (error: any) => {
      console.error('API Error:', error);
      toast.error(error?.response?.data?.message || 'Invalid invitation code');
    },
  });

  const onSubmit = async () => {
    if (pin.length !== 6) {
      toast.error('Please enter a valid 6-digit invitation code.');
      return;
    }

    console.log('Submitting invitation code:', pin);
    try {
      const result = await verifyInvitationMutation.mutateAsync(pin);
      console.log('Mutation result:', result);
    } catch (error) {
      console.error('Error verifying invitation code:', error);
    }
  };
  return (
    <div className="relative flex min-h-screen w-full items-center justify-center">
      <ImageComponent
        src="/assets/images/vfbg.png"
        figClassName="!absolute w-full h-full"
        fill
        className="object-cover"
      />
      <div className="absolute h-full w-full bg-black/40"></div>
      <div className="relative z-20 mx-auto flex max-w-[700px] flex-col items-center justify-center rounded-lg bg-white p-5 shadow-xl sm:p-10 lg:p-20">
        <h2 className="h1 text-center !font-display !text-3xl text-primary sm:!text-5xl">
          Enter your Invitation Code
        </h2>
        <p className="mt-5 max-w-[350px] text-center text-black dark:text-white sm:mt-10 sm:!text-2xl">
          Please enter the 6-digit code
        </p>

        {/* {!showResend && countdown}

      {showResend && (
        <p className="mt-5">
          Didn&apos;t receive the code?{' '}
          <span
            onClick={resetCountdown}
            className="cursor-pointer border-b border-primary pb-[2px] text-primary"
          >
            Click to resend.
          </span>
        </p>
      )} */}
        <div className="mx-auto mt-10 grid grid-cols-6 gap-3">
          <PinField
            type="tel"
            length={6}
            onComplete={(p) => {
              if (/^\d{6}$/.test(p)) {
                setPin(p);
              } else {
                toast.error('Please enter a valid 6-digit code.');
              }
            }}
            onChange={(value) => {
              if (/^[0-9]*$/.test(value)) {
                handlePinChange(value);
              }
            }} // Trigger onChange when input changes
            className={`h-[65px] w-[65px] overflow-y-auto rounded-md border-4 bg-white text-center text-2xl font-bold !text-black md:rounded-xl xs:h-[45px] xs:w-[45px] xs:p-1.5 ${
              isFilled ? 'border-primary' : 'dark:border-[#D0D0D0]'
            }`} // Conditional border color based on input
          />
        </div>
        <Button
          type="submit"
          disabled={pin.length < 6}
          onClick={onSubmit}
          isLoading={verifyInvitationMutation.isPending}
          className="mt-10 w-full"
        >
          Submit
        </Button>
      </div>
    </div>
  );
};

export default UserVerification;
