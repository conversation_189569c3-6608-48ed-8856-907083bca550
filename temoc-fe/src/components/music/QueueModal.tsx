'use client';
import React from 'react';
import { useMusicPlayer } from '@/context/MusicPlayerContext';
import { FaTimes, FaPlay, FaPause } from 'react-icons/fa';
import { IoReorderThree } from 'react-icons/io5';
import ImageComponent from '@/components/common/ImageComponent';

const QueueModal = () => {
  const {
    queue,
    currentIndex,
    currentTrack,
    isPlaying,
    showQueue,
    setShowQueue,
    playTrack,
    pauseTrack,
    resumeTrack,
    removeFromQueue,
    clearQueue,
  } = useMusicPlayer();

  if (!showQueue) return null;

  const formatTime = (time: number): string => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const handleTrackPlay = (track: any, index: number) => {
    console.log(index);

    if (currentTrack?._id === track._id) {
      if (isPlaying) {
        pauseTrack();
      } else {
        resumeTrack();
      }
    } else {
      playTrack(track, queue);
    }
  };

  const upcomingTracks = queue.slice(currentIndex + 1);
  const currentPlayingTrack = queue[currentIndex];

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 z-50 bg-black bg-opacity-50"
        onClick={() => setShowQueue(false)}
      />

      {/* Enhanced Modal */}
      <div className="fixed bottom-20 right-4 top-4 z-50 w-96 rounded-2xl border border-gray-200 bg-white/95 shadow-2xl backdrop-blur-md">
        {/* Enhanced Header */}
        <div className="flex items-center justify-between border-b border-gray-200/50 p-6">
          <h2 className="text-xl font-bold text-gray-900">Next up</h2>
          <div className="flex items-center space-x-3">
            <button
              onClick={clearQueue}
              className="rounded-lg bg-red-50 px-3 py-1 text-sm font-medium text-red-600 transition-all hover:bg-red-100"
            >
              Clear
            </button>
            <button
              onClick={() => setShowQueue(false)}
              className="rounded-full p-2 text-gray-500 transition-all hover:bg-gray-100 hover:text-gray-700"
            >
              <FaTimes className="text-base" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex h-full flex-col overflow-hidden">
          {/* Enhanced Currently Playing */}
          {currentPlayingTrack && (
            <div className="border-b border-gray-100/50 p-6">
              <h3 className="mb-4 text-sm font-bold uppercase tracking-wider text-gray-700">
                Now Playing
              </h3>
              <div className="flex items-center space-x-4 rounded-xl bg-gradient-to-r from-primary/10 to-orange-100 p-4">
                <div className="relative h-14 w-14 flex-shrink-0 overflow-hidden rounded-xl shadow-lg">
                  <ImageComponent
                    src={
                      currentPlayingTrack.thumbnailUrl ||
                      '/assets/images/women.avif'
                    }
                    alt={currentPlayingTrack.title}
                    className="h-full w-full object-cover"
                    fill
                  />
                  <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-40">
                    <button
                      onClick={() =>
                        handleTrackPlay(currentPlayingTrack, currentIndex)
                      }
                      className="flex h-8 w-8 items-center justify-center rounded-full bg-white text-black shadow-lg transition-transform hover:scale-110"
                    >
                      {isPlaying &&
                      currentTrack?._id === currentPlayingTrack._id ? (
                        <FaPause className="text-sm" />
                      ) : (
                        <FaPlay className="ml-0.5 text-sm" />
                      )}
                    </button>
                  </div>
                </div>
                <div className="min-w-0 flex-1">
                  <p className="truncate text-base font-bold text-gray-900">
                    {currentPlayingTrack.title}
                  </p>
                  <p className="truncate text-sm text-gray-600">
                    {currentPlayingTrack.artist ||
                      currentPlayingTrack.creator.displayName}
                  </p>
                </div>
                <div className="text-sm font-medium text-gray-600">
                  {formatTime(currentPlayingTrack.duration)}
                </div>
              </div>
            </div>
          )}

          {/* Enhanced Queue List */}
          <div className="flex-1 overflow-y-auto">
            {upcomingTracks.length > 0 ? (
              <div className="p-6">
                <h3 className="mb-4 text-sm font-bold uppercase tracking-wider text-gray-700">
                  Next ({upcomingTracks.length})
                </h3>
                <div className="space-y-3">
                  {upcomingTracks.map((track, index) => {
                    const actualIndex = currentIndex + 1 + index;
                    return (
                      <div
                        key={`${track._id}-${actualIndex}`}
                        className="group flex cursor-pointer items-center space-x-4 rounded-xl p-3 transition-all hover:bg-gray-50"
                        onClick={() => handleTrackPlay(track, actualIndex)}
                      >
                        {/* Drag Handle */}
                        <div className="cursor-move text-gray-400 opacity-0 transition-opacity group-hover:opacity-100">
                          <IoReorderThree className="text-xl" />
                        </div>

                        {/* Enhanced Track Image */}
                        <div className="relative h-12 w-12 flex-shrink-0 overflow-hidden rounded-xl shadow-md">
                          <ImageComponent
                            src={
                              track.thumbnailUrl || '/assets/images/women.avif'
                            }
                            alt={track.title}
                            className="h-full w-full object-cover transition-transform group-hover:scale-110"
                            fill
                          />
                          <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-40 opacity-0 transition-opacity group-hover:opacity-100">
                            <div className="flex h-6 w-6 items-center justify-center rounded-full bg-primary text-white shadow-lg">
                              <FaPlay className="ml-0.5 text-xs" />
                            </div>
                          </div>
                        </div>

                        {/* Enhanced Track Info */}
                        <div className="min-w-0 flex-1">
                          <p className="truncate text-sm font-semibold text-gray-900 transition-colors group-hover:text-primary">
                            {track.title}
                          </p>
                          <p className="truncate text-xs text-gray-600">
                            {track.artist || track.creator.displayName}
                          </p>
                        </div>

                        {/* Duration */}
                        <div className="text-xs font-medium text-gray-500">
                          {formatTime(track.duration)}
                        </div>

                        {/* Remove Button */}
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            removeFromQueue(actualIndex);
                          }}
                          className="rounded-full p-2 text-gray-400 opacity-0 transition-all hover:bg-red-50 hover:text-red-600 group-hover:opacity-100"
                        >
                          <FaTimes className="text-xs" />
                        </button>
                      </div>
                    );
                  })}
                </div>
              </div>
            ) : (
              <div className="flex h-full items-center justify-center p-8">
                <div className="text-center">
                  <div className="mb-2 text-4xl text-gray-300">🎵</div>
                  <p className="text-sm text-gray-500">No tracks in queue</p>
                  <p className="text-xs text-gray-400">
                    Add songs to see them here
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default QueueModal;
