'use client';
import React, { useState } from 'react';
import { useMusicPlayer } from '@/context/MusicPlayerContext';
import {
  FaPlay,
  FaPause,
  FaStepForward,
  FaStepBackward,
  FaVolumeUp,
  FaList,
  FaTimes,
} from 'react-icons/fa';
import { IoShuffle, IoRepeat } from 'react-icons/io5';
import ImageComponent from '@/components/common/ImageComponent';

const GlobalMusicPlayer = () => {
  const {
    currentTrack,
    isPlaying,
    currentTime,
    duration,
    volume,
    queue,
    currentIndex,
    playTrack,
    pauseTrack,
    resumeTrack,
    nextTrack,
    previousTrack,
    seekTo,
    setVolume,
    toggleShuffle,
    toggleLoop,
    isShuffled,
    isLooping,
    showPlayer,
    showQueue,
    setShowQueue,
    clearQueue,
    setShowTrackPage,
  } = useMusicPlayer();

  const [showVolumeSlider, setShowVolumeSlider] = useState(false);

  // Get next track in queue
  const nextTrackInQueue = queue[currentIndex + 1];

  if (!showPlayer || !currentTrack) return null;

  const formatTime = (time: number): string => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const handleProgressClick = (e: React.MouseEvent<HTMLDivElement>) => {
    const rect = e.currentTarget.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const newTime = (clickX / rect.width) * duration;
    seekTo(newTime);
  };

  const handlePlayPause = () => {
    if (isPlaying) {
      pauseTrack();
    } else {
      resumeTrack();
    }
  };

  const handleTrackClick = () => {
    setShowTrackPage(true);
  };

  return (
    <>
      {/* Enhanced Main Player Bar */}
      <div className="fixed bottom-0 left-0 right-0 z-50 border-t border-gray-200/50 bg-white/95 shadow-2xl backdrop-blur-md">
        <div className="mx-auto max-w-full px-6 py-4">
          <div className="flex items-center justify-between">
            {/* Enhanced Track Info */}
            <div className="flex min-w-0 flex-1 items-center space-x-4">
              <div
                className="group relative h-14 w-14 flex-shrink-0 cursor-pointer overflow-hidden rounded-xl bg-gray-200 shadow-lg transition-all hover:scale-110 hover:shadow-xl"
                onClick={handleTrackClick}
              >
                <ImageComponent
                  src={currentTrack.thumbnailUrl || '/assets/images/women.avif'}
                  alt={currentTrack.title}
                  className="h-full w-full rounded-lg object-cover"
                  fill
                  figClassName="h-12 w-12 rounded-lg"
                />
                <div className="absolute inset-0 bg-black/20 opacity-0 transition-opacity group-hover:opacity-100" />
              </div>
              <div className="min-w-0 flex-1">
                <p
                  className="cursor-pointer truncate text-base font-semibold text-gray-900 transition-colors hover:text-primary"
                  onClick={handleTrackClick}
                >
                  {currentTrack.title}
                </p>
                <p className="truncate text-sm text-gray-600">
                  {currentTrack.artist || currentTrack.creator.displayName}
                </p>
              </div>

              {/* Next Track Preview */}
              {nextTrackInQueue && (
                <div
                  className="hidden cursor-pointer items-center space-x-3 rounded-lg bg-gray-50 p-2 transition-all hover:bg-gray-100 lg:flex"
                  onClick={() => playTrack(nextTrackInQueue, queue)}
                >
                  <div className="text-xs font-medium text-gray-500">Next:</div>
                  <div className="h-8 w-8 overflow-hidden rounded-lg">
                    <ImageComponent
                      src={
                        nextTrackInQueue.thumbnailUrl ||
                        '/assets/images/women.avif'
                      }
                      alt={nextTrackInQueue.title}
                      className="h-full w-full object-cover"
                      fill
                    />
                  </div>
                  <div className="min-w-0">
                    <p className="max-w-[120px] truncate text-xs font-medium text-gray-800">
                      {nextTrackInQueue.title}
                    </p>
                  </div>
                  <div className="flex h-6 w-6 items-center justify-center rounded-full bg-primary/20 text-primary">
                    <FaPlay className="ml-0.5 text-xs" />
                  </div>
                </div>
              )}
            </div>

            {/* Enhanced Player Controls */}
            <div className="flex flex-1 flex-col items-center space-y-3">
              {/* Control Buttons */}
              <div className="flex items-center space-x-6">
                <button
                  onClick={toggleShuffle}
                  className={`rounded-full p-2 transition-all hover:scale-110 ${
                    isShuffled
                      ? 'bg-primary/10 text-primary'
                      : 'text-gray-500 hover:bg-gray-100 hover:text-gray-700'
                  }`}
                >
                  <IoShuffle className="text-base" />
                </button>

                <button
                  onClick={previousTrack}
                  className="rounded-full p-2 text-gray-600 transition-all hover:scale-110 hover:bg-gray-100 hover:text-gray-900"
                >
                  <FaStepBackward className="text-base" />
                </button>

                <button
                  onClick={handlePlayPause}
                  className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-r from-primary to-orange-600 text-white shadow-lg transition-all hover:scale-110 hover:shadow-xl"
                >
                  {isPlaying ? (
                    <FaPause className="text-lg" />
                  ) : (
                    <FaPlay className="ml-0.5 text-lg" />
                  )}
                </button>

                <button
                  onClick={nextTrack}
                  className="rounded-full p-2 text-gray-600 transition-all hover:scale-110 hover:bg-gray-100 hover:text-gray-900"
                >
                  <FaStepForward className="text-base" />
                </button>

                <button
                  onClick={toggleLoop}
                  className={`rounded-full p-2 transition-all hover:scale-110 ${
                    isLooping
                      ? 'bg-primary/10 text-primary'
                      : 'text-gray-500 hover:bg-gray-100 hover:text-gray-700'
                  }`}
                >
                  <IoRepeat className="text-base" />
                </button>
              </div>

              {/* Enhanced Progress Bar */}
              <div className="flex w-full max-w-lg items-center space-x-3">
                <span className="min-w-[35px] text-xs font-medium text-gray-600">
                  {formatTime(currentTime)}
                </span>
                <div
                  className="relative h-2 flex-1 cursor-pointer rounded-full bg-gray-200 shadow-inner"
                  onClick={handleProgressClick}
                >
                  <div
                    className="absolute h-full rounded-full bg-gradient-to-r from-primary to-orange-600 shadow-sm transition-all"
                    style={{
                      width: `${duration ? (currentTime / duration) * 100 : 0}%`,
                    }}
                  />
                  <div
                    className="absolute top-1/2 h-4 w-4 -translate-y-1/2 rounded-full border-2 border-primary bg-white opacity-0 shadow-lg transition-all hover:opacity-100"
                    style={{
                      left: `${duration ? (currentTime / duration) * 100 : 0}%`,
                    }}
                  />
                </div>
                <span className="min-w-[35px] text-xs font-medium text-gray-600">
                  {formatTime(duration)}
                </span>
              </div>
            </div>

            {/* Enhanced Right Controls */}
            <div className="flex min-w-0 flex-1 items-center justify-end space-x-4">
              {/* Volume Control */}
              <div className="relative">
                <button
                  onClick={() => setShowVolumeSlider(!showVolumeSlider)}
                  className="rounded-full p-2 text-gray-600 transition-all hover:scale-110 hover:bg-gray-100 hover:text-gray-900"
                >
                  <FaVolumeUp className="text-base" />
                </button>
                {showVolumeSlider && (
                  <div className="absolute bottom-full right-0 mb-3 rounded-xl border border-gray-200 bg-white/95 p-4 shadow-xl backdrop-blur-md">
                    <input
                      type="range"
                      min="0"
                      max="1"
                      step="0.01"
                      value={volume}
                      onChange={(e) => setVolume(parseFloat(e.target.value))}
                      className="h-24 w-2 cursor-pointer appearance-none rounded-full bg-gray-200 outline-none"
                      // @ts-ignore
                      style={{ writingMode: 'bt-lr' }}
                    />
                  </div>
                )}
              </div>

              {/* Queue Button */}
              <button
                onClick={() => setShowQueue(!showQueue)}
                className={`rounded-full p-2 transition-all hover:scale-110 ${
                  showQueue
                    ? 'bg-primary/10 text-primary'
                    : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                }`}
              >
                <FaList className="text-base" />
              </button>

              {/* Close Button */}
              <button
                onClick={clearQueue}
                className="rounded-full p-2 text-gray-600 transition-all hover:scale-110 hover:bg-red-50 hover:text-red-600"
              >
                <FaTimes className="text-base" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Volume Slider Backdrop */}
      {showVolumeSlider && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setShowVolumeSlider(false)}
        />
      )}
    </>
  );
};

export default GlobalMusicPlayer;
