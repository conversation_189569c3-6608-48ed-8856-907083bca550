'use client';
import React, { useState, useEffect } from 'react';
import { useMusicPlayer } from '@/context/MusicPlayerContext';
import { FaPlay, FaPause, FaArrowLeft, FaShare } from 'react-icons/fa';
import { IoAdd, IoRepeat, IoShuffle } from 'react-icons/io5';
import ImageComponent from '@/components/common/ImageComponent';
import { generateGradientFromImage } from '@/utils/colorExtractor';
import { useQuery } from '@tanstack/react-query';
import { discoveryApi } from '@/services/discovery';
import { contentApi } from '@/services/content';
import Skeleton from 'react-loading-skeleton';
import { useFollowSync } from '@/hooks/useFollowSync';
import { FollowButton } from '@/components/social/FollowButton';

// Artist sidebar item component
// const ArtistSidebarItem = ({ artist }: { artist: any }) => {
//   const { isFollowing, isLoading, handleFollowToggle, loadingAction } =
//     useFollowSync({
//       userId: artist._id,
//       displayName: artist.displayName,
//       initialFollowStatus: artist.isFollowing,
//     });

//   return (
//     <div className="flex items-center space-x-3 rounded-lg p-2 transition-colors hover:bg-gray-700">
//       <div className="h-10 w-10 overflow-hidden rounded-full">
//         <ImageComponent
//           src={
//             artist.artistProfile?.profilePic ||
//             artist.avatarUrl ||
//             '/assets/images/women.avif'
//           }
//           alt={artist.displayName}
//           className="h-full w-full object-cover"
//           fill
//         />
//       </div>
//       <div className="min-w-0 flex-1">
//         <p className="truncate text-sm font-medium text-white">
//           {artist.displayName}
//         </p>
//         <p className="truncate text-xs text-gray-400">
//           {artist.followersCount || 0} followers
//         </p>
//       </div>
//       <FollowButton
//         isFollowing={isFollowing}
//         isLoading={isLoading}
//         onClick={handleFollowToggle}
//         loadingAction={loadingAction || undefined}
//         size="sm"
//         className="!px-2 !py-1 !text-xs"
//       />
//     </div>
//   );
// };

const TrackDetailPage = () => {
  const {
    currentTrack,
    isPlaying,
    currentTime,
    duration,
    showTrackPage,
    setShowTrackPage,
    playTrack,
    pauseTrack,
    resumeTrack,
    addToQueue,
    nextTrack,
    previousTrack,
    toggleShuffle,
    toggleLoop,
    isShuffled,
    isLooping,
  } = useMusicPlayer();

  const [gradientBackground, setGradientBackground] = useState('');

  // Use the existing follow sync hook
  const {
    isFollowing,
    isLoading: isFollowLoading,
    handleFollowToggle,
    loadingAction,
  } = useFollowSync({
    userId: currentTrack?.creator?._id || '',
    displayName: currentTrack?.creator?.displayName || currentTrack?.artist,
    initialFollowStatus: undefined, // Let the hook fetch the status
  });

  // Fetch popular artists for sidebar
  const { data: popularArtists, isLoading: artistsLoading } = useQuery({
    queryKey: ['popular-artists-track-page'],
    queryFn: () => discoveryApi.getPopularArtists(8),
    enabled: showTrackPage,
  });

  // Fetch recommended tracks
  const { data: recommendedTracks, isLoading: tracksLoading } = useQuery({
    queryKey: ['recommended-tracks'],
    queryFn: () => contentApi.getTrendingSongs(),
    enabled: showTrackPage,
  });

  // Generate gradient from track thumbnail
  useEffect(() => {
    if (currentTrack?.thumbnailUrl) {
      generateGradientFromImage(currentTrack.thumbnailUrl).then((gradient) => {
        setGradientBackground(gradient);
      });
    }
  }, [currentTrack]);

  if (!showTrackPage || !currentTrack) return null;

  const formatTime = (time: number): string => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const handlePlayPause = () => {
    if (isPlaying) {
      pauseTrack();
    } else {
      resumeTrack();
    }
  };

  const progressPercentage = duration ? (currentTime / duration) * 100 : 0;
  console.log(currentTrack);

  return (
    <div className="fixed inset-0 z-50 bg-gradient-to-br from-gray-900 via-gray-800 to-black">
      {/* Header */}
      <div className="flex h-16 items-center justify-between border-b border-gray-700/50 bg-black/20 px-6 backdrop-blur-md">
        <button
          onClick={() => setShowTrackPage(false)}
          className="flex h-8 w-8 items-center justify-center rounded-full bg-white/10 text-white transition-all hover:scale-105 hover:bg-white/20"
        >
          <FaArrowLeft className="text-sm" />
        </button>
        <h1 className="text-lg font-semibold text-white">Now Playing</h1>
        <div className="w-8" /> {/* Spacer */}
      </div>

      <div className="flex h-[calc(100vh-4rem)]">
        {/* Main Content */}
        <div className="flex-1 overflow-y-auto">
          {/* Track Header with Enhanced Gradient */}
          <div
            className="relative h-80 p-8"
            style={{
              background:
                gradientBackground ||
                'linear-gradient(135deg, #FF6B00 0%, #FF8C00 30%, #E55A00 70%, #1a1a1a 100%)',
            }}
          >
            <div className="absolute inset-0 bg-black/20" />
            <div className="relative flex h-full items-center space-x-8">
              {/* Enhanced Track Artwork */}
              <div className="group relative h-56 w-56 flex-shrink-0">
                <div className="h-full w-full overflow-hidden rounded-2xl shadow-2xl ring-4 ring-white/20 transition-transform group-hover:scale-105">
                  <ImageComponent
                    src={
                      currentTrack?.thumbnailUrl || '/assets/images/women.avif'
                    }
                    alt={currentTrack?.title || 'Track'}
                    className="h-full w-full object-cover transition-transform duration-500 group-hover:scale-110"
                    fill
                  />
                </div>
                {/* Reflection effect */}
                <div className="absolute -bottom-4 left-0 h-16 w-full overflow-hidden rounded-b-2xl opacity-30">
                  <ImageComponent
                    src={
                      currentTrack?.thumbnailUrl || '/assets/images/women.avif'
                    }
                    alt={currentTrack?.title || 'Track'}
                    className="h-full w-full scale-y-[-1] object-cover blur-sm"
                    fill
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent" />
                </div>
              </div>

              {/* Enhanced Track Details */}
              <div className="min-w-0 flex-1 space-y-4">
                <div>
                  <p className="text-sm font-semibold uppercase tracking-wider text-white/70">
                    TRACK
                  </p>
                  <h1 className="mt-2 text-4xl font-bold text-white drop-shadow-lg md:text-6xl lg:text-7xl">
                    {currentTrack?.title || 'Unknown Track'}
                  </h1>
                </div>
                <div className="flex items-center space-x-3 text-white/90">
                  <div className="h-8 w-8 overflow-hidden rounded-full ring-2 ring-white/30">
                    <ImageComponent
                      src={
                        currentTrack?.creator?.avatarUrl ||
                        '/assets/images/women.avif'
                      }
                      alt={currentTrack?.creator?.displayName || 'Artist'}
                      className="h-full w-full object-cover"
                      fill
                    />
                  </div>
                  <span className="text-lg font-semibold">
                    {currentTrack?.artist ||
                      currentTrack?.creator?.displayName ||
                      'Unknown Artist'}
                  </span>
                  <span className="text-white/60">•</span>
                  <span className="text-sm font-medium">
                    {currentTrack?.playCount || 0} plays
                  </span>
                  <span className="text-white/60">•</span>
                  <span className="text-sm font-medium">
                    {formatTime(duration)}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Enhanced Player Controls */}
          <div className="bg-black/30 p-8 backdrop-blur-md">
            <div className="flex items-center justify-between">
              {/* Left Controls */}
              <div className="flex items-center space-x-6">
                <button
                  onClick={previousTrack}
                  className="p-3 text-white/70 transition-all hover:scale-110 hover:text-white"
                >
                  <FaArrowLeft className="text-lg" />
                </button>

                <button
                  onClick={handlePlayPause}
                  className="flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-r from-primary to-orange-600 text-white shadow-lg transition-all hover:scale-110 hover:shadow-xl"
                >
                  {isPlaying ? (
                    <FaPause className="text-xl" />
                  ) : (
                    <FaPlay className="ml-1 text-xl" />
                  )}
                </button>

                <button
                  onClick={nextTrack}
                  className="p-3 text-white/70 transition-all hover:scale-110 hover:text-white"
                >
                  <FaArrowLeft className="rotate-180 text-lg" />
                </button>

                <button
                  onClick={toggleShuffle}
                  className={`p-3 transition-all hover:scale-110 ${
                    isShuffled
                      ? 'text-primary'
                      : 'text-white/70 hover:text-white'
                  }`}
                >
                  <IoShuffle className="text-lg" />
                </button>

                <button
                  onClick={toggleLoop}
                  className={`p-3 transition-all hover:scale-110 ${
                    isLooping
                      ? 'text-primary'
                      : 'text-white/70 hover:text-white'
                  }`}
                >
                  <IoRepeat className="text-lg" />
                </button>
              </div>

              {/* Right Actions */}
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => addToQueue(currentTrack)}
                  className="p-3 text-white/70 transition-all hover:scale-110 hover:text-white"
                >
                  <IoAdd className="text-xl" />
                </button>

                <button className="p-3 text-white/70 transition-all hover:scale-110 hover:text-white">
                  <FaShare className="text-xl" />
                </button>
              </div>
            </div>

            {/* Enhanced Progress Bar */}
            <div className="mt-6">
              <div className="relative h-2 w-full overflow-hidden rounded-full bg-white/20">
                <div
                  className="absolute h-full rounded-full bg-gradient-to-r from-primary to-orange-600 shadow-lg transition-all"
                  style={{ width: `${progressPercentage}%` }}
                />
                <div
                  className="absolute top-1/2 h-4 w-4 -translate-y-1/2 rounded-full bg-white shadow-lg transition-all"
                  style={{ left: `${progressPercentage}%` }}
                />
              </div>
              <div className="mt-3 flex justify-between text-sm text-white/80">
                <span>{formatTime(currentTime)}</span>
                <span>{formatTime(duration)}</span>
              </div>
            </div>
          </div>

          {/* Main Content Area with Artist Info */}
          <div className="flex-1 bg-gradient-to-b from-gray-900/50 to-black p-8">
            {/* Artist Info Section */}
            <div className="mb-12">
              <h3 className="mb-6 text-2xl font-bold text-white">Artist</h3>
              <div className="flex items-center space-x-6 rounded-2xl bg-white/5 p-6 backdrop-blur-md transition-all hover:bg-white/10">
                <div className="h-20 w-20 overflow-hidden rounded-full ring-4 ring-primary/30">
                  <ImageComponent
                    src={
                      currentTrack?.creator?.avatarUrl ||
                      '/assets/images/women.avif'
                    }
                    alt={currentTrack?.creator?.displayName || 'Artist'}
                    className="h-full w-full object-cover"
                    fill
                  />
                </div>
                <div className="flex-1">
                  <p className="text-xl font-bold text-white">
                    {currentTrack?.creator?.displayName || 'Unknown Artist'}
                  </p>
                  <p className="text-sm text-white/60">
                    @{currentTrack?.creator?.username || 'unknown'}
                  </p>
                  <p className="mt-2 text-sm text-white/80">
                    {/* // @ts-ignore */}
                    {currentTrack?.creator?.followersCount || 0} followers
                  </p>
                </div>
                <FollowButton
                  isFollowing={isFollowing}
                  isLoading={isFollowLoading}
                  onClick={handleFollowToggle}
                  loadingAction={loadingAction || undefined}
                  size="lg"
                />
              </div>
            </div>

            {/* Recommended Tracks Section */}
            <div>
              <h3 className="mb-6 text-2xl font-bold text-white">
                Recommended Tracks
              </h3>
              <div className="space-y-3">
                {tracksLoading
                  ? Array.from({ length: 6 }).map((_, i) => (
                      <div
                        key={i}
                        className="flex items-center space-x-4 rounded-xl bg-white/5 p-4"
                      >
                        <Skeleton
                          height={60}
                          width={60}
                          baseColor="#374151"
                          highlightColor="#4B5563"
                        />
                        <div className="flex-1">
                          <Skeleton
                            height={16}
                            width="70%"
                            baseColor="#374151"
                            highlightColor="#4B5563"
                          />
                          <Skeleton
                            height={12}
                            width="50%"
                            className="mt-2"
                            baseColor="#374151"
                            highlightColor="#4B5563"
                          />
                        </div>
                      </div>
                    ))
                  : recommendedTracks?.slice(0, 8).map((track: any) => (
                      <div
                        key={track._id}
                        className="group flex cursor-pointer items-center space-x-4 rounded-xl bg-white/5 p-4 transition-all hover:bg-white/10"
                        onClick={() =>
                          playTrack(track as any, recommendedTracks as any)
                        }
                      >
                        <div className="relative h-16 w-16 overflow-hidden rounded-lg">
                          <ImageComponent
                            src={
                              track.thumbnailUrl || '/assets/images/women.avif'
                            }
                            alt={track.title}
                            className="h-full w-full object-cover transition-transform group-hover:scale-110"
                            fill
                          />
                          <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-40 opacity-0 transition-opacity group-hover:opacity-100">
                            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-white">
                              <FaPlay className="ml-0.5 text-sm" />
                            </div>
                          </div>
                        </div>
                        <div className="min-w-0 flex-1">
                          <p className="truncate text-lg font-semibold text-white transition-colors group-hover:text-primary">
                            {track.title}
                          </p>
                          <p className="truncate text-sm text-white/60">
                            {track.artist || track.creator.displayName}
                          </p>
                        </div>
                        <div className="text-sm text-white/50">
                          {track.playCount || 0} plays
                        </div>
                      </div>
                    ))}
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Sidebar - Now showing Popular Artists only */}
        <div className="w-80 border-l border-white/10 bg-black/20 p-6 backdrop-blur-md">
          {/* Popular Artists */}
          <div>
            <h3 className="mb-6 text-xl font-bold text-white">
              Popular Artists
            </h3>
            <div className="space-y-4">
              {artistsLoading
                ? Array.from({ length: 8 }).map((_, i) => (
                    <div
                      key={i}
                      className="flex items-center space-x-4 rounded-xl bg-white/5 p-3"
                    >
                      <Skeleton
                        circle
                        height={50}
                        width={50}
                        baseColor="#374151"
                        highlightColor="#4B5563"
                      />
                      <div className="flex-1">
                        <Skeleton
                          height={16}
                          width="70%"
                          baseColor="#374151"
                          highlightColor="#4B5563"
                        />
                        <Skeleton
                          height={12}
                          width="50%"
                          className="mt-2"
                          baseColor="#374151"
                          highlightColor="#4B5563"
                        />
                      </div>
                      <Skeleton
                        height={32}
                        width={70}
                        baseColor="#374151"
                        highlightColor="#4B5563"
                      />
                    </div>
                  ))
                : popularArtists?.slice(0, 8).map((artist: any) => (
                    <div
                      key={artist._id}
                      className="group flex cursor-pointer items-center space-x-4 rounded-xl bg-white/5 p-3 transition-all hover:bg-white/10"
                    >
                      <div className="h-12 w-12 overflow-hidden rounded-full ring-2 ring-primary/30 transition-all group-hover:ring-primary/60">
                        <ImageComponent
                          src={artist.avatarUrl || '/assets/images/women.avif'}
                          alt={artist.displayName}
                          className="h-full w-full object-cover"
                          fill
                        />
                      </div>
                      <div className="flex-1">
                        <p className="font-semibold text-white transition-colors group-hover:text-primary">
                          {artist.displayName}
                        </p>
                        <p className="text-xs text-white/60">
                          {artist.followersCount || 0} followers
                        </p>
                      </div>
                      <button className="rounded-lg bg-primary/20 px-3 py-1 text-xs font-medium text-primary transition-all hover:bg-primary hover:text-white">
                        Follow
                      </button>
                    </div>
                  ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TrackDetailPage;
